import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { subCategories } from "@/constants";
import Link from "next/link";
import React from "react";

export default function SubCategoryGroup({ category }) {
  return (
    <Card className="w-full p-6 rounded-2xl border-2 shadow">
      <CardHeader>
        <CardTitle className={"text-2xl!"}> {category.name}</CardTitle>
      </CardHeader>
      <CardContent className={"w-full"}>
        <div className="w-[90%] grid grid-cols-3 gap-4">
          {subCategories
            .filter((subCategory) => subCategory.categories.id === category.id)
            .map((subCategory) => (
              <Link
                key={subCategory.id}
                href={"/vendors/listing"}
                className="p-1 flex flex-col items-center justify-center"
              >
                <ImageWithFallback
                  src={subCategory.coverImage}
                  className="w-[160px] h-[100px] rounded-md object-cover"
                />
                <h3 className="text-sm font-semibold text-center mt-2">
                  {subCategory.name}
                </h3>
              </Link>
            ))}
        </div>
      </CardContent>
    </Card>
  );
}
