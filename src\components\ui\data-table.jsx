import * as React from "react";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, ChevronLeft, ChevronRight, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "./table";

export function DataTable({
  data,
  columns,
  loading,
  displayButtons = true,
  displayFilters = true,
  additionalFilters = "",
}) {
  const [sorting, setSorting] = React.useState([]);
  const [columnFilters, setColumnFilters] = React.useState([]);
  const [columnVisibility, setColumnVisibility] = React.useState({});
  const [rowSelection, setRowSelection] = React.useState({});

  const filterableColumns = React.useMemo(() => {
    return columns
      .filter(
        (column) =>
          column.id !== "actions" &&
          column.filterable &&
          column.enableHiding !== false &&
          typeof column.accessorKey === "string"
      )
      .map((column) => ({
        id: column.id || column.accessorKey,
        label: column.header?.toString() || column.accessorKey,
      }));
  }, [columns]);

  const [selectedColumn, setSelectedColumn] = React.useState(undefined);

  React.useEffect(() => {
    if (filterableColumns.length > 0 && !selectedColumn) {
      setSelectedColumn(filterableColumns[0].id);
    }
  }, [filterableColumns, selectedColumn]);

  const table = useReactTable({
    data: data || [],
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full">
      <div className="flex flex-row-reverse items-center gap-4 py-4">
        {selectedColumn && displayFilters && (
          <>
            {additionalFilters}

            <Select value={selectedColumn} onValueChange={setSelectedColumn}>
              <SelectTrigger className="w-[180px] bg-muted">
                <SelectValue placeholder="Select column to filter" />
              </SelectTrigger>
              <SelectContent>
                {filterableColumns.map((column) => (
                  <SelectItem key={column.id} value={column.id}>
                    {column.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <div className="flex-1 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={`Filter by ${
                  filterableColumns
                    .find((col) => col.id === selectedColumn)
                    ?.label.toLowerCase() || "column"
                }...`}
                value={
                  selectedColumn
                    ? table?.getColumn(selectedColumn)?.getFilterValue() ?? ""
                    : ""
                }
                onChange={(event) => {
                  table
                    ?.getColumn(selectedColumn)
                    ?.setFilterValue(event?.target?.value);
                }}
                className="pl-8 w-full bg-muted"
              />
            </div>
          </>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => (
                <DropdownMenuCheckboxItem
                  key={column.id}
                  className="capitalize"
                  checked={column.getIsVisible()}
                  onCheckedChange={(value) => column.toggleVisibility(!!value)}
                >
                  {column.id}
                </DropdownMenuCheckboxItem>
              ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="rounded-md border overflow-hidden">
        <Table className="w-full text-sm">
          <TableHeader className={"bg-muted"}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow className={"border-b font-bold"} key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="border-b">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} className="hover:bg-muted/50 border-b">
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="border-b">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="text-center h-24"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {displayButtons && (
        <div className="flex items-center justify-between space-x-4 py-4">
          <div className="text-sm text-muted-foreground">
            Showing results {table.getRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} on Page (
            {table.getState().pagination.pageIndex + 1})
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft size={18} />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <ChevronRight size={18} />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
