import React from "react";
import {
  Car<PERSON>ian<PERSON>rid,
  <PERSON><PERSON><PERSON>s,
  ResponsiveContainer,
  YA<PERSON>s,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from "recharts";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Database } from "lucide-react";

const chartConfig = {
  paid: {
    label: "Paid",
    color: "var(--chart-2)",
  },
  failed: {
    label: "Failed",
    color: "var(--chart-5)",
  },
  refunded: {
    label: "Refunded",
    color: "var(--chart-3)",
  },
};

export function GrowthChart({ chartData, dateFilter }) {
  return (
    <Card className={"my-4"}>
      <CardHeader>
        <CardTitle>Payment Status Chart</CardTitle>
        <CardDescription>
          Showing payment status distribution for the {dateFilter.preset}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {chartData?.length > 0 ? (
          <ChartContainer
            config={chartConfig}
            style={{ width: "100%", height: 250 }}
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                accessibilityLayer
                data={chartData}
                margin={{
                  left: 24,
                  right: 12,
                  top: 10,
                  bottom: 10,
                }}
              >
                <CartesianGrid />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <YAxis tickLine={false} axisLine={false} tickMargin={8} />
                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
                <Bar dataKey="paid" fill="var(--chart-1)" radius={4} />
                <Bar dataKey="failed" fill="var(--chart-2)" radius={4} />
                <Bar dataKey="refunded" fill="var(--chart-3)" radius={4} />{" "}
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        ) : (
          <div className="flex justify-center items-center h-60">
            <div className="flex flex-col items-center justify-center">
              <Database className="w-20 h-20 mx-auto mb-4 opacity-50" />
              <CardTitle>Oops! Data not Found</CardTitle>
              <CardDescription>
                No data found, try changing the date filter.
              </CardDescription>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
