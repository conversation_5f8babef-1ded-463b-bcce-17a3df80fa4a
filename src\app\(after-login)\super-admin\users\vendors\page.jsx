"use client";

import { useMemo, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Building2,
  CheckCircle,
  Edit,
  Filter,
  Search,
  XCircle,
} from "lucide-react";
import { pendingVendors as vendorConstants } from "@/constants/vendors";

export default function VendorsPage() {
  const [items, setItems] = useState(
    vendorConstants.map((v) => ({ ...v, decision: "pending" }))
  );
  const [query, setQuery] = useState("");
  const [status, setStatus] = useState("all");
  const [page, setPage] = useState(1);
  const pageSize = 5;

  const filtered = useMemo(() => {
    let data = [...items];
    if (query)
      data = data.filter((v) =>
        [v.businessName, v.contactPerson, v.category, v.location]
          .join(" ")
          .toLowerCase()
          .includes(query.toLowerCase())
      );
    if (status !== "all") data = data.filter((v) => v.decision === status);
    return data;
  }, [items, query, status]);

  const pageCount = Math.max(1, Math.ceil(filtered.length / pageSize));
  const pageItems = filtered.slice((page - 1) * pageSize, page * pageSize);

  const [activeVendor, setActiveVendor] = useState(null);
  const [dialog, setDialog] = useState(null); // 'edit' | 'docs' | 'approve' | 'reject' | null
  const closeDialog = () => {
    setDialog(null);
    setActiveVendor(null);
  };

  const approve = () => {
    setItems((prev) =>
      prev.map((it) =>
        it.id === activeVendor.id ? { ...it, decision: "approved" } : it
      )
    );
    closeDialog();
  };
  const reject = () => {
    setItems((prev) =>
      prev.map((it) =>
        it.id === activeVendor.id ? { ...it, decision: "rejected" } : it
      )
    );
    closeDialog();
  };

  return (
    <div className="min-h-screen bg-background">
      <main className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Vendor Management</h1>
            <div className="flex gap-2">
              <Button variant="outline" className="rounded-xl">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button className="rounded-xl bg-primary hover:bg-primary/90">
                Export List
              </Button>
            </div>
          </div>

          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search vendors..."
                className="pl-10 rounded-xl"
                value={query}
                onChange={(e) => {
                  setQuery(e.target.value);
                  setPage(1);
                }}
              />
            </div>
            <Select
              value={status}
              onValueChange={(v) => {
                setStatus(v);
                setPage(1);
              }}
            >
              <SelectTrigger className="w-40 rounded-xl">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            {pageItems.map((vendor) => (
              <Card key={vendor.id} className="p-6 rounded-2xl">
                <div className="space-y-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                        <Building2 className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">
                          {vendor.businessName}
                        </h3>
                        <p className="text-muted-foreground">
                          Contact: {vendor.contactPerson}
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                          <span>{vendor.category}</span>
                          <span>•</span>
                          <span>{vendor.location}</span>
                          <span>•</span>
                          <span>Registered by {vendor.registeredBy}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-100 text-green-800">
                        Payment: {vendor.paymentStatus}
                      </Badge>
                      <Badge variant="outline">{vendor.documents} docs</Badge>
                      {vendor.decision !== "pending" && (
                        <Badge
                          className={
                            vendor.decision === "approved"
                              ? "bg-green-600"
                              : "bg-red-600"
                          }
                        >
                          {vendor.decision}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-3">
                    <Button
                      size="sm"
                      className="rounded-lg bg-green-600 hover:bg-green-700"
                      onClick={() => {
                        setActiveVendor(vendor);
                        setDialog("approve");
                      }}
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Approve Vendor
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="rounded-lg"
                      onClick={() => {
                        setActiveVendor(vendor);
                        setDialog("reject");
                      }}
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Reject
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="rounded-lg"
                      onClick={() => {
                        setActiveVendor(vendor);
                        setDialog("edit");
                      }}
                    >
                      <Edit className="w-4 h-4 mr-2" />
                      Edit Details
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="rounded-lg"
                      onClick={() => {
                        setActiveVendor(vendor);
                        setDialog("docs");
                      }}
                    >
                      View Documents
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => setPage((p) => Math.max(1, p - 1))}
                />
              </PaginationItem>
              {Array.from({ length: pageCount }).map((_, i) => (
                <PaginationItem key={i}>
                  <PaginationLink
                    isActive={page === i + 1}
                    onClick={() => setPage(i + 1)}
                  >
                    {i + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}
              <PaginationItem>
                <PaginationNext
                  onClick={() => setPage((p) => Math.min(pageCount, p + 1))}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </main>

      {/* Approve/Reject/Edit/Docs Dialogs */}
      <Dialog open={!!dialog} onOpenChange={(o) => !o && closeDialog()}>
        {dialog === "approve" && activeVendor && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Approve {activeVendor.businessName}?</DialogTitle>
            </DialogHeader>
            <p className="text-sm text-muted-foreground">
              This will mark the vendor as approved.
            </p>
            <DialogFooter>
              <Button variant="outline" onClick={closeDialog}>
                Cancel
              </Button>
              <Button onClick={approve}>Approve</Button>
            </DialogFooter>
          </DialogContent>
        )}
        {dialog === "reject" && activeVendor && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reject {activeVendor.businessName}?</DialogTitle>
            </DialogHeader>
            <p className="text-sm text-muted-foreground">
              This will mark the vendor as rejected.
            </p>
            <DialogFooter>
              <Button variant="outline" onClick={closeDialog}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={reject}>
                Reject
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
        {dialog === "docs" && activeVendor && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Documents</DialogTitle>
            </DialogHeader>
            <p className="text-sm">
              {activeVendor.documents} document(s) uploaded.
            </p>
            <DialogFooter>
              <Button onClick={closeDialog}>Close</Button>
            </DialogFooter>
          </DialogContent>
        )}
        {dialog === "edit" && activeVendor && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Vendor Details</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div>
                <label className="text-sm">Business Name</label>
                <Input defaultValue={activeVendor.businessName} />
              </div>
              <div>
                <label className="text-sm">Contact Person</label>
                <Input defaultValue={activeVendor.contactPerson} />
              </div>
              <div>
                <label className="text-sm">Category</label>
                <Input defaultValue={activeVendor.category} />
              </div>
              <div>
                <label className="text-sm">Location</label>
                <Input defaultValue={activeVendor.location} />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={closeDialog}>
                Cancel
              </Button>
              <Button onClick={closeDialog}>Save</Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </div>
  );
}
