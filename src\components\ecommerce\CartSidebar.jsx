"use client";

import React from "react";
import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { useCart } from "@/contexts/CartContext";
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Trash2, 
  X,
  ArrowRight
} from "lucide-react";
import Link from "next/link";

export function CartSidebar({ children }) {
  const { 
    items, 
    cartItemsCount, 
    cartSubtotal, 
    cartTax, 
    cartShipping, 
    cartGrandTotal,
    updateQuantity, 
    removeFromCart 
  } = useCart();

  return (
    <Sheet>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            Shopping Cart
            {cartItemsCount > 0 && (
              <Badge variant="secondary">{cartItemsCount}</Badge>
            )}
          </SheetTitle>
        </SheetHeader>

        <div className="flex flex-col h-full">
          {items.length === 0 ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ShoppingCart className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Your cart is empty
                </h3>
                <p className="text-gray-600 mb-4">
                  Add some products to get started
                </p>
                <Link href="/search">
                  <Button>Browse Products</Button>
                </Link>
              </div>
            </div>
          ) : (
            <>
              {/* Cart Items */}
              <div className="flex-1 overflow-y-auto py-4">
                <div className="space-y-4">
                  {items.map((item) => (
                    <CartItem
                      key={item.id}
                      item={item}
                      onUpdateQuantity={updateQuantity}
                      onRemove={removeFromCart}
                    />
                  ))}
                </div>
              </div>

              {/* Cart Summary */}
              <div className="border-t pt-4 space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium">${cartSubtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Tax</span>
                    <span className="font-medium">${cartTax.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Shipping</span>
                    <span className="font-medium">
                      {cartShipping === 0 ? "Free" : `$${cartShipping.toFixed(2)}`}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>${cartGrandTotal.toFixed(2)}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2">
                  <Link href="/cart" className="block">
                    <Button variant="outline" className="w-full">
                      View Cart
                    </Button>
                  </Link>
                  <Button className="w-full">
                    Checkout
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>

                {/* Free Shipping Notice */}
                {cartShipping > 0 && (
                  <div className="text-sm text-center text-blue-600 bg-blue-50 p-2 rounded">
                    Add ${(50 - cartSubtotal).toFixed(2)} more for free shipping!
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}

// Cart Item Component
function CartItem({ item, onUpdateQuantity, onRemove }) {
  return (
    <div className="flex gap-3">
      {/* Product Image */}
      <div className="flex-shrink-0">
        <ImageWithFallback
          src={item.product.images[0]}
          className="w-16 h-16 object-cover rounded-lg"
          alt={item.product.name}
        />
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-900 line-clamp-2">
              {item.product.name}
            </h4>
            <p className="text-xs text-gray-600 mt-1">{item.product.brand}</p>
            
            {/* Selected Options */}
            {(item.selectedSize || item.selectedColor) && (
              <div className="flex gap-2 mt-1">
                {item.selectedSize && (
                  <span className="text-xs text-gray-500">
                    Size: {item.selectedSize}
                  </span>
                )}
                {item.selectedColor && (
                  <span className="text-xs text-gray-500">
                    Color: {item.selectedColor}
                  </span>
                )}
              </div>
            )}

            {/* Price */}
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm font-semibold text-gray-900">
                ${item.product.price}
              </span>
              {item.product.originalPrice > item.product.price && (
                <span className="text-xs text-gray-500 line-through">
                  ${item.product.originalPrice}
                </span>
              )}
            </div>
          </div>

          {/* Remove Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemove(item.id)}
            className="text-gray-400 hover:text-red-600 p-1"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Quantity Controls */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onUpdateQuantity(item.id, item.quantity - 1)}
              disabled={item.quantity <= 1}
              className="h-6 w-6 p-0"
            >
              <Minus className="w-3 h-3" />
            </Button>
            <span className="text-sm font-medium w-8 text-center">
              {item.quantity}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
              className="h-6 w-6 p-0"
            >
              <Plus className="w-3 h-3" />
            </Button>
          </div>
          
          <div className="text-sm font-semibold text-gray-900">
            ${(item.product.price * item.quantity).toFixed(2)}
          </div>
        </div>
      </div>
    </div>
  );
}

export default CartSidebar;
