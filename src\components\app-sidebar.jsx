"use client";

import * as React from "react";
import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import { SidebarLogo } from "@/components/sidebar-logo";
import {
  Sidebar,
  SidebarContent,
  <PERSON>barFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { getNavigationForRole } from "@/constants/navLinks";
import { getCurrentUserRole } from "@/utils/roleUtils";

export function AppSidebar({ role, ...props }) {
  // Get navigation data based on the user's role
  // If no role is provided, try to get it from the current user
  const userRole = role || getCurrentUserRole();
  const navigationData = getNavigationForRole(userRole);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarLogo user={navigationData.user} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navigationData.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={navigationData.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
