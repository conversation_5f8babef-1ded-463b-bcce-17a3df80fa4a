"use client";

import { paymentsDone } from "@/constants/sales";
import StatsCard from "./components/stats";
import { categories, displayVendors, systemUsers } from "@/constants";
import { DataTable } from "@/components/ui/data-table";
import { Badge } from "@/components/ui/badge";
import ActionComponent from "@/components/ui/action-component";

const columns = [
  {
    id: "id",
    accessorKey: "vendor.name",
    header: "Vendor Name",
    filterable: true,
    cell: ({ row }) => <div>{row.original?.vendor?.name}</div>,
  },
  {
    id: "referredBy",
    accessorKey: "referredBy.name",
    header: "Sales Executive",
    filterable: true,
    cell: ({ row }) => <div> {row?.original?.referredBy?.name} </div>,
  },
  {
    id: "categories",
    accessorKey: "categories.name",
    header: "Category",
    filterable: true,
    cell: ({ row }) => <div>{row?.original?.categories?.name}</div>,
  },
  {
    id: "subCategories",
    accessorKey: "subCategories",
    header: "Sub Category",
    filterable: false,
    cell: ({ row }) => (
      <div className="grid gap-2">
        {row?.original?.subCategories.map((sub) => (
          <Badge key={sub.id} variant="outline">
            {sub.name}
          </Badge>
        ))}
      </div>
    ),
  },
  {
    id: "plan",
    accessorKey: "plan",
    header: "Plan",
    filterable: true,
    cell: ({ row }) => <div>{row?.original?.plan}</div>,
  },
  {
    id: "commission",
    accessorKey: "commission",
    header: "Commission",
    filterable: false,
    cell: ({ row }) => (
      <div>
        {Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(row?.original?.commission)}
      </div>
    ),
  },
  {
    id: "Date",
    accessorKey: "date",
    header: "Date",
    filterable: false,
    cell: ({ row }) => (
      <div>
        {new Date(row?.original?.date).toLocaleDateString("en-US", {
          day: "numeric",
          month: "short",
          year: "numeric",
        })}
      </div>
    ),
  },
  {
    id: "actions",
    accessorKey: "actions",
    header: "Actions",
    filterable: false,
    cell: ({ row }) => <ActionComponent />,
  },
];

export default function SalesCommissionsPage() {
  const users = systemUsers.filter((u) => u.role === "sales_executive");
  return (
    <div className="max-w-7xl mx-auto p-6">
      <h1 className="text-2xl font-bold">Sales Commissions</h1>
      <p className="text-muted-foreground pb-4">
        Track and manage sales commissions for vendors
      </p>

      {/* Stats Cards */}
      <StatsCard
        vendors={displayVendors}
        categories={categories}
        salesExecs={users}
        payments={paymentsDone}
      />

      <DataTable data={paymentsDone} columns={columns} />
    </div>
  );
}
