"use client";

import { useEffect, useState } from "react";
import { DataTable } from "@/components/ui/data-table";
import { Badge } from "@/components/ui/badge";
import ActionComponent from "@/components/ui/action-component";
import DateFilter from "@/components/blocks/DateFilter";
import { displayRevenueData } from "@/constants/adminReports";
import { toast } from "sonner";
import { Download, DollarSign, CircleCheckBig } from "lucide-react";
import StatsCard from "./components/stats";
import { RevenueChart } from "./components/chart";
import { Button } from "@/components/ui/button";

export default function RevenueReportPage() {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState("all");
  const [chartData, setChartData] = useState([]);

  // Date filter state
  const [dateFilter, setDateFilter] = useState({
    startDate: "",
    endDate: "",
    preset: "week", // Default to last 7 days
  });

  // Function to apply date filtering
  const applyDateFilter = (_data, filter) => {
    if (!filter.startDate && !filter.endDate && filter.preset === "week") {
      // Default behavior - last 7 days
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const filtered_data = _data.filter((item) => {
        const itemDate = new Date(item?.created);
        return itemDate >= weekAgo;
      });

      return filtered_data;
    }

    if (!filter.startDate && !filter.endDate) {
      return _data;
    }

    const startDate = filter.startDate ? new Date(filter.startDate) : null;
    const endDate = filter.endDate ? new Date(filter.endDate) : null;

    const filtered_data = _data.filter((item) => {
      const itemDate = new Date(item?.created);
      if (startDate && itemDate < startDate) return false;
      if (endDate && itemDate > endDate) return false;
      return true;
    });

    return filtered_data;
  };

  // Function to handle date filter changes
  const handleDateFilterChange = (newFilter) => {
    setDateFilter(newFilter);
  };

  // Generate Chart Data for Pie Chart
  const generateChartData = (revenues) => {
    const planRevenue = {};

    revenues.forEach((revenue) => {
      const planName = revenue.planName;
      if (!planRevenue[planName]) {
        planRevenue[planName] = { value: 0, count: 0 };
      }
      planRevenue[planName].value += revenue.amount;
      planRevenue[planName].count += 1;
    });

    const result = Object.entries(planRevenue).map(([name, data]) => ({
      name,
      value: data.value,
      count: data.count,
    }));

    setChartData(result);
  };

  const handleExportCSV = () => {
    if (!filteredData || filteredData.length === 0) {
      toast.warning("No data to export.");
      return;
    }

    // Define CSV headers
    const headers = [
      "Vendor Name",
      "Vendor Email",
      "Plan Name",
      "Plan Type",
      "Amount",
      "Currency",
      "Payment Date",
      "Payment Method",
      "Transaction ID",
      "Commission",
      "Net Revenue",
      "Created Date",
    ];

    // Map data rows
    const rows = filteredData.map((revenue) => [
      revenue.vendorName || "",
      revenue.vendorEmail || "",
      revenue.planName || "",
      revenue.planType || "",
      revenue.amount || "",
      revenue.currency || "",
      revenue.paymentDate || "",
      revenue.paymentMethod || "",
      revenue.transactionId || "",
      revenue.commission || "",
      revenue.netRevenue || "",
      revenue.created
        ? new Date(revenue.created).toLocaleDateString("en-US", {
            day: "numeric",
            month: "short",
            year: "numeric",
          })
        : "",
    ]);

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map((e) =>
        e
          .map((field) => `"${String(field).replace(/"/g, '""')}"`) // Escape quotes
          .join(",")
      )
      .join("\n");

    // Create a blob and trigger download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.setAttribute("href", url);
    link.setAttribute("download", "revenue_report.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Data fetching
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const records = displayRevenueData;
        setData(records);
        setDateFilter({ ...dateFilter, preset: "week" });
      } catch (error) {
        console.log("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const filtered_data = applyDateFilter(data, dateFilter);
        switch (filter) {
          case "starter":
            const starter_data = filtered_data.filter(
              (r) => r.planName === "Starter"
            );
            setFilteredData(starter_data);
            generateChartData(starter_data);
            break;

          case "pro":
            const pro_data = filtered_data.filter((r) => r.planName === "Pro");
            setFilteredData(pro_data);
            generateChartData(pro_data);
            break;

          case "enterprise":
            const enterprise_data = filtered_data.filter(
              (r) => r.planName === "Enterprise"
            );
            setFilteredData(enterprise_data);
            generateChartData(enterprise_data);
            break;

          default:
            setFilteredData(filtered_data);
            generateChartData(filtered_data);
            break;
        }
      } catch (error) {
        toast.error("Failed to fetch data:");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [dateFilter, filter]);

  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const columns = [
    {
      id: "vendorName",
      accessorKey: "vendorName",
      header: "Vendor Name",
      filterable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <p>{row.original?.vendorName}</p>
        </div>
      ),
    },
    {
      id: "email",
      accessorKey: "vendorEmail",
      header: "Email",
      filterable: true,
      cell: ({ row }) => (
        <div className="truncate w-48">{row.original?.vendorEmail}</div>
      ),
    },
    {
      id: "plan",
      accessorKey: "planName",
      header: "Plan",
      filterable: true,
      cell: ({ row }) => (
        <div className="flex flex-col">
          <Badge variant="secondary">{row.original?.planName}</Badge>
          <span className="text-xs text-muted-foreground capitalize">
            {row.original?.planType}
          </span>
        </div>
      ),
    },
    {
      id: "amount",
      accessorKey: "amount",
      header: "Amount",
      filterable: false,
      cell: ({ row }) => (
        <div className="font-semibold">
          {formatCurrency(row.original?.amount, row.original?.currency)}
        </div>
      ),
    },
    {
      id: "commission",
      accessorKey: "commission",
      header: "Commission",
      filterable: false,
      cell: ({ row }) => (
        <div className="font-semibold text-green-600">
          {formatCurrency(row.original?.commission, row.original?.currency)}
        </div>
      ),
    },
    {
      id: "netRevenue",
      accessorKey: "netRevenue",
      header: "Net Revenue",
      filterable: false,
      cell: ({ row }) => (
        <div className="font-semibold text-blue-600">
          {formatCurrency(row.original?.netRevenue, row.original?.currency)}
        </div>
      ),
    },
    {
      id: "paymentMethod",
      accessorKey: "paymentMethod",
      header: "Payment Method",
      filterable: true,
      cell: ({ row }) => (
        <Badge variant="outline" className="capitalize">
          {row.original?.paymentMethod}
        </Badge>
      ),
    },
    {
      id: "transactionId",
      accessorKey: "transactionId",
      header: "Transaction ID",
      filterable: true,
      cell: ({ row }) => (
        <div className="font-mono text-xs">{row.original?.transactionId}</div>
      ),
    },
    {
      id: "createdDate",
      accessorKey: "created",
      header: "Date",
      filterable: false,
      cell: ({ row }) =>
        row.original?.created ? (
          <div>
            {new Date(row.original.created).toLocaleDateString("en-US", {
              day: "numeric",
              month: "short",
              year: "numeric",
            })}
          </div>
        ) : (
          "-"
        ),
    },
    {
      id: "actions",
      accessorKey: "actions",
      header: "Actions",
      filterable: false,
      cell: ({ row }) => <ActionComponent />,
    },
  ];

  return (
    <div className="flex items-center justify-center p-6">
      <div className="w-[90%]">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-bold">Welcome back!</h2>
            <p className="text-sm text-muted-foreground">
              Here's a list of all Revenue Records.
            </p>
          </div>

          <div className="flex gap-4 items-end">
            <DateFilter
              dateFilter={dateFilter}
              onFilterChange={handleDateFilterChange}
            />
            {filteredData?.length > 0 && (
              <Button onClick={handleExportCSV}>
                Export CSV <Download className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </div>

        <StatsCard revenues={filteredData} setFilter={setFilter} />
        {/* <RevenueChart chartData={chartData} dateFilter={dateFilter} /> */}
        <DataTable columns={columns} data={filteredData} />
      </div>
    </div>
  );
}
