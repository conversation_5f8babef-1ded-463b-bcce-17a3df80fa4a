// Reports-oriented sample data
import { customers } from "./customers";
import { PLANS } from "./plans";
import { displayVendors } from "./vendors";
export const plansPaymentsReport = {
  payments: [
    {
      id: 201,
      vendor: "Giuseppe's Italian Kitchen",
      plan: "Starter",
      amount: 99,
      date: "2024-01-15",
      status: "paid",
    },
    {
      id: 202,
      vendor: "TechFix Solutions",
      plan: "Starter",
      amount: 99,
      date: "2024-01-14",
      status: "paid",
    },
    {
      id: 203,
      vendor: "Green Garden Landscaping",
      plan: "Starter",
      amount: 99,
      date: "2024-01-12",
      status: "paid",
    },
  ],
  totals: { paid: 297, failed: 0, refunded: 0 },
};

// Enhanced plans-payments data for detailed reporting
export const displayPlansPaymentsData = [
  {
    id: 1,
    vendor: displayVendors[0],
    plan: PLANS[0],
    status: "paid",
    paymentMethod: "Credit Card",
    transactionId: "TXN001ABC",
    created: "2025-03-12T10:30:00Z",
  },
  {
    id: 2,
    vendor: displayVendors[1],
    plan: PLANS[1],
    status: "paid",
    paymentMethod: "Debit Card",
    transactionId: "TXN002DEF",
    created: "2025-06-18T14:20:00Z",
  },
  {
    id: 3,
    vendor: displayVendors[2],
    plan: PLANS[2],
    status: "failed",
    paymentMethod: "Credit Card",
    transactionId: "TXN003GHI",
    created: "2025-02-09T09:15:00Z",
  },
  {
    id: 4,
    vendor: displayVendors[0],
    plan: PLANS[0],
    status: "paid",
    paymentMethod: "Bank Transfer",
    transactionId: "TXN004JKL",
    created: "2025-07-21T16:45:00Z",
  },
  {
    id: 5,
    vendor: displayVendors[0],
    plan: PLANS[0],
    status: "refunded",
    paymentMethod: "Credit Card",
    transactionId: "TXN005MNO",
    created: "2025-04-05T11:30:00Z",
  },
  {
    id: 6,
    vendor: displayVendors[1],
    plan: PLANS[1],
    status: "paid",
    paymentMethod: "PayPal",
    transactionId: "TXN006PQR",
    created: "2025-08-15T13:20:00Z",
  },
  {
    id: 7,
    vendor: displayVendors[2],
    plan: PLANS[2],
    status: "failed",
    paymentMethod: "Credit Card",
    transactionId: "TXN007STU",
    created: "2025-05-27T08:45:00Z",
  },
  {
    id: 8,
    vendor: displayVendors[1],
    plan: PLANS[1],
    status: "paid",
    paymentMethod: "Credit Card",
    transactionId: "TXN008VWX",
    created: "2025-09-03T15:10:00Z",
  },
  {
    id: 9,
    vendor: displayVendors[0],
    plan: PLANS[0],
    status: "paid",
    paymentMethod: "Debit Card",
    transactionId: "TXN009YZA",
    created: "2025-10-22T12:25:00Z",
  },
  {
    id: 10,
    vendor: displayVendors[2],
    plan: PLANS[2],
    status: "paid",
    paymentMethod: "Bank Transfer",
    transactionId: "TXN010BCD",
    created: "2025-11-30T17:30:00Z",
  },
];

export const vendorsOnboardingReport = {
  series: [
    { day: "Mon", onboarded: 4 },
    { day: "Tue", onboarded: 6 },
    { day: "Wed", onboarded: 5 },
    { day: "Thu", onboarded: 7 },
    { day: "Fri", onboarded: 8 },
    { day: "Sat", onboarded: 3 },
    { day: "Sun", onboarded: 2 },
  ],
};

export const engagementReport = {
  metrics: [
    { name: "Daily Active Users", value: 1240 },
    { name: "Weekly Active Users", value: 5320 },
    { name: "Avg. Session (min)", value: 5.6 },
  ],
  notifications: [
    { id: 1, title: "New review posted", time: "2h" },
    { id: 2, title: "Vendor responded to enquiry", time: "5h" },
  ],
};

// Enhanced engagement data for detailed reporting
export const displayEngagementData = [
  {
    id: 1,
    activityType: "enquiry_sent",
    page: "/vendors/123",
    deviceType: "desktop",
    location: "New York",
    users: customers[0],
    created: "2024-11-15T10:30:00Z",
  },
  {
    id: 2,
    activityType: "enquiry_sent",
    page: "/vendors/123",
    deviceType: "mobile",
    location: "Los Angeles",
    users: customers[1],
    created: "2025-01-14T14:20:00Z",
  },
  {
    id: 3,
    activityType: "review_posted",
    page: "/vendors/456",
    deviceType: "tablet",
    location: "Chicago",
    users: customers[2],
    created: "2025-04-13T09:15:00Z",
  },
  {
    id: 4,
    activityType: "review_posted",
    page: "/vendors/1432",
    deviceType: "desktop",
    location: "Houston",
    users: customers[0],
    created: "2025-03-12T16:45:00Z",
  },
  {
    id: 5,
    activityType: "favorite_added",
    page: "/vendors/1432",
    deviceType: "mobile",
    location: "Phoenix",
    users: customers[0],
    created: "2025-02-11T11:30:00Z",
  },
  {
    id: 6,
    activityType: "favorite_added",
    page: "/vendors/789",
    deviceType: "desktop",
    location: "Philadelphia",
    users: customers[1],
    created: "2025-05-10T13:20:00Z",
  },
  {
    id: 7,
    activityType: "favorite_added",
    page: "/vendors/321",
    deviceType: "mobile",
    location: "San Antonio",
    users: customers[2],
    created: "2025-06-09T08:45:00Z",
  },
  {
    id: 8,
    activityType: "enquiry_sent",
    page: "/vendors/654",
    deviceType: "tablet",
    location: "San Diego",
    users: customers[1],
    created: "2025-07-08T15:10:00Z",
  },
  {
    id: 9,
    activityType: "enquiry_sent",
    page: "/dashboard",
    deviceType: "desktop",
    location: "Dallas",
    users: customers[2],
    created: "2025-02-11T11:30:00Z",
  },
  {
    id: 10,
    activityType: "review_posted",
    page: "/vendors/987",
    deviceType: "mobile",
    location: "San Jose",
    users: customers[0],
    created: "2025-08-06T17:30:00Z",
  },
];
