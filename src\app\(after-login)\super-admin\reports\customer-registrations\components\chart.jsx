import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Database } from "lucide-react";

const chartConfig = {
  active: {
    label: "Active",
    color: "var(--chart-2)",
  },
  inactive: {
    label: "Inactive",
    color: "var(--chart-5)",
  },
};

export function GrowthChart({ chartData, dateFilter }) {
  return (
    <Card className={"my-4"}>
      <CardHeader>
        <CardTitle>Customer Registration Growth</CardTitle>
        <CardDescription>
          Showing customer registrations for the {dateFilter.preset}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {chartData?.length > 0 ? (
          <ChartContainer
            config={chartConfig}
            style={{ width: "100%", height: 250 }}
          >
            <ResponsiveContainer width="100%" height={250}>
              <BarChart
                data={chartData}
                margin={{ left: 24, right: 12, top: 10, bottom: 10 }}
              >
                <CartesianGrid />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <YAxis tickLine={false} axisLine={false} tickMargin={8} />

                <Bar
                  dataKey="active"
                  stackId="a"
                  fill={chartConfig.active.color}
                  radius={[0, 0, 4, 4]}
                />
                <Bar
                  dataKey="inactive"
                  stackId="a"
                  fill={chartConfig.inactive.color}
                  radius={[4, 4, 0, 0]}
                />

                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        ) : (
          <div className="flex justify-center items-center h-60">
            <div className="flex flex-col items-center justify-center">
              <Database className="w-20 h-20 mx-auto mb-4 opacity-50" />
              <CardTitle>Oops! Data not Found</CardTitle>
              <CardDescription>
                No data found, try changing the date filter.
              </CardDescription>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
