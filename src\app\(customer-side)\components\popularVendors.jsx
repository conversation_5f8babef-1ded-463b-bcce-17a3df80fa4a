"use client";

import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import {
  Star,
  TrendingUp,
  MapPin,
  Phone,
  MessageCircle,
  Eye,
  Grid3X3,
  List,
} from "lucide-react";
import { displayVendors } from "@/constants";
import { useRouter } from "next/navigation";
import { VendorDetailModal } from "@/components/blocks/VendorDetailModel";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";

export default function PopularVendors() {
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [viewMode, setViewMode] = useState("grid");
  const [isLoggedIn, setisLoggedIn] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const isLoggedIn = localStorage?.getItem("isLoggedIn") || false;
    if (isLoggedIn) {
      setisLoggedIn(true);
    }
  }, []);

  const handlePushLogin = () => {
    router.push("/login");
  };

  return (
    <main className="border-t bg-background py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Toolbar */}
        <div className="flex items-center justify-between gap-8 mb-6">
          <h1 className="text-2xl md:text-3xl font-semibold">
            Popular Vendors in Thane
          </h1>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-3">
            <div className="flex items-center border rounded-lg">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-r-none"
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none"
              >
                <List className="w-4 h-4" />
              </Button>
            </div>

            <Link href="/vendors/listing">
              <Button variant={"link"}>View All</Button>
            </Link>
          </div>
        </div>

        <Separator />

        {/* Vendor Cards */}
        {viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-12">
            {displayVendors
              ?.sort((a, b) => b.rating - a.rating)
              ?.slice(0, 3)
              ?.map((v) => (
                <GridViewCard
                  key={v.id}
                  vendor={v}
                  isLoggedIn={isLoggedIn}
                  handlePushLogin={handlePushLogin}
                  setSelectedVendor={setSelectedVendor}
                />
              ))}
          </div>
        ) : (
          <div className="space-y-6 pt-12">
            {displayVendors
              ?.sort((a, b) => b.rating - a.rating)
              ?.slice(0, 3)
              ?.map((v) => (
                <ListViewCard
                  key={v.id}
                  vendor={v}
                  isLoggedIn={isLoggedIn}
                  handlePushLogin={handlePushLogin}
                  setSelectedVendor={setSelectedVendor}
                />
              ))}
          </div>
        )}
      </div>

      {selectedVendor && (
        <VendorDetailModal
          vendor={selectedVendor}
          onClose={() => setSelectedVendor(null)}
        />
      )}
    </main>
  );
}

const ListViewCard = ({
  vendor,
  handlePushLogin,
  setSelectedVendor,
  isLoggedIn,
}) => {
  return (
    <Card className="p-4 md:p-5 rounded-2xl shadow-sm">
      <div className="grid grid-cols-1 sm:grid-cols-[160px_1fr] gap-4">
        {/* Image */}
        <div className="rounded-xl overflow-hidden border bg-card">
          <div className="aspect-[4/4]">
            <ImageWithFallback
              src={vendor.image}
              alt={vendor.name}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col justify-between">
          <div className="space-y-2">
            <div className="flex items-center flex-wrap gap-3">
              <h3 className="text-lg md:text-xl font-semibold text-foreground">
                {vendor.name}
              </h3>
              <Badge className="bg-primary/15 text-primary border-primary/20 rounded-lg">
                <span className="flex items-center gap-1">
                  <Star className="w-4 h-4 fill-primary text-primary" />{" "}
                  {vendor.rating}
                </span>
              </Badge>
              {vendor.trending && (
                <Badge variant="secondary" className="rounded-lg">
                  <TrendingUp className="w-4 h-4 mr-1" /> Trending
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2 text-muted-foreground">
              <MapPin className="w-4 h-4" />
              <span>{vendor.address}</span>
              <span className="mx-1">•</span>
              <span>{vendor.distance}</span>
            </div>

            <div className="flex flex-wrap gap-2 pt-1">
              {vendor.categories.map((c) => (
                <Badge key={c} variant="secondary" className="rounded-lg">
                  {c}
                </Badge>
              ))}
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-3">
            {isLoggedIn ? (
              <Button variant="outline" className="rounded-xl">
                <Phone className="w-4 h-4 mr-2" /> {vendor.phone}
              </Button>
            ) : (
              <>
                <Button
                  variant="outline"
                  className="rounded-xl"
                  onClick={handlePushLogin}
                >
                  <Phone className="w-4 h-4 mr-2" /> Contact
                </Button>
                <Button
                  variant="outline"
                  className="rounded-xl"
                  onClick={handlePushLogin}
                >
                  <MessageCircle className="w-4 h-4 mr-2" /> WhatsApp
                </Button>
              </>
            )}
            <Button
              onClick={() => setSelectedVendor(vendor)}
              className="rounded-xl bg-primary hover:bg-primary/90"
            >
              <Eye className="w-4 h-4 ml-2" />
              View Details
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export const GridViewCard = ({
  vendor,
  handlePushLogin,
  setSelectedVendor,
  isLoggedIn,
}) => {
  return (
    <Card className="rounded-2xl overflow-hidden shadow-sm">
      {/* Image */}
      <div className="overflow-hidden border bg-card -mt-6">
        <div className="aspect-[4/2]">
          <ImageWithFallback
            src={vendor.image}
            alt={vendor.name}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      <CardContent className={"-pt-2"}>
        <div className="grid grid-cols-1 gap-4">
          {/* Content */}
          <div className="flex flex-col justify-between">
            <div className="space-y-2">
              <div className="flex items-center flex-wrap gap-3">
                <h3 className="text-lg md:text-xl font-semibold text-foreground">
                  {vendor.name}
                </h3>
                <Badge className="bg-primary/15 text-primary border-primary/20 rounded-lg">
                  <span className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-primary text-primary" />{" "}
                    {vendor.rating}
                  </span>
                </Badge>
              </div>

              <div className="flex items-center gap-2 text-muted-foreground">
                <MapPin className="w-4 h-4" />
                <span>{vendor.address}</span>
                <span className="mx-1">•</span>
                <span>{vendor.distance}</span>
              </div>

              <div className="flex flex-wrap gap-2 pt-1">
                {vendor.categories.map((c) => (
                  <Badge key={c} variant="secondary" className="rounded-lg">
                    {c}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="mt-4 flex flex-wrap gap-3">
              {isLoggedIn ? (
                <Button variant="outline" className="rounded-xl">
                  <Phone className="w-4 h-4 mr-2" /> {vendor.phone}
                </Button>
              ) : (
                <>
                  <Button
                    variant="outline"
                    className="rounded-xl"
                    onClick={handlePushLogin}
                  >
                    <Phone className="w-4 h-4 mr-2" /> Contact
                  </Button>
                  <Button
                    variant="outline"
                    className="rounded-xl"
                    onClick={handlePushLogin}
                  >
                    <MessageCircle className="w-4 h-4 mr-2" /> WhatsApp
                  </Button>
                </>
              )}
              <Button
                onClick={() => setSelectedVendor(vendor)}
                className="rounded-xl bg-primary hover:bg-primary/90"
              >
                <Eye className="w-4 h-4 ml-2" />
                View Details
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
