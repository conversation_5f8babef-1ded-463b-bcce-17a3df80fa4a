import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  X,
  Star,
  MapPin,
  Phone,
  Mail,
  Globe,
  Clock,
  Heart,
  Share2,
  Camera,
  Send,
  CheckCircle,
  Calendar,
} from "lucide-react";
import { ImageWithFallback } from "../ui/image-fallback";

export function VendorDetailModal({ vendor, onClose }) {
  const [activeTab, setActiveTab] = useState("overview");
  const [enquiryMessage, setEnquiryMessage] = useState("");
  const [isLiked, setIsLiked] = useState(false);
  const [selectedImage, setSelectedImage] = useState(0);

  const reviews = [
    {
      id: 1,
      customer: "<PERSON>",
      rating: 5,
      comment:
        "Absolutely amazing service! The team was professional and delivered exactly what we needed. Highly recommended!",
      date: "2 weeks ago",
    },
    {
      id: 2,
      customer: "Michael Chen",
      rating: 4,
      comment:
        "Great experience overall. Good quality work and reasonable pricing. Will definitely use their services again.",
      date: "1 month ago",
    },
    {
      id: 3,
      customer: "Sarah Williams",
      rating: 5,
      comment:
        "Exceeded our expectations! Very responsive and professional throughout the entire process.",
      date: "1 month ago",
    },
  ];

  const gallery = [
    "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800",
    "https://images.unsplash.com/photo-1514933651103-005eec06c04b?w=800",
    "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800",
    "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=800",
  ];

  const services = [
    "Business Consultation",
    "Strategic Planning",
    "Market Analysis",
    "Financial Planning",
    "Risk Assessment",
  ];

  const hours = {
    Monday: "9:00 AM - 6:00 PM",
    Tuesday: "9:00 AM - 6:00 PM",
    Wednesday: "9:00 AM - 6:00 PM",
    Thursday: "9:00 AM - 6:00 PM",
    Friday: "9:00 AM - 6:00 PM",
    Saturday: "10:00 AM - 4:00 PM",
    Sunday: "Closed",
  };

  const handleSendEnquiry = () => {
    if (enquiryMessage.trim()) {
      setEnquiryMessage("");
    }
  };

  const tabs = [
    { id: "overview", label: "Overview" },
    { id: "gallery", label: "Gallery" },
    { id: "reviews", label: "Reviews" },
    { id: "contact", label: "Contact" },
  ];

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Description */}
      <div>
        <h3 className="font-semibold mb-3">About</h3>
        <p className="text-muted-foreground leading-relaxed">
          {vendor.description ||
            "We are a professional service provider dedicated to delivering exceptional results. Our experienced team combines expertise with innovative solutions to meet your business needs. With years of industry experience, we pride ourselves on quality, reliability, and customer satisfaction."}
        </p>
      </div>

      {/* Services */}
      <div>
        <h3 className="font-semibold mb-3">Services</h3>
        <div className="flex flex-wrap gap-2">
          {services.map((service, index) => (
            <Badge
              key={index}
              variant="secondary"
              className="bg-primary/10 text-primary"
            >
              {service}
            </Badge>
          ))}
        </div>
      </div>

      {/* Business Hours */}
      <div>
        <h3 className="font-semibold mb-3">Business Hours</h3>
        <div className="space-y-2">
          {Object.entries(hours).map(([day, time]) => (
            <div
              key={day}
              className="flex justify-between items-center p-2 rounded-lg hover:bg-muted/50"
            >
              <span className="font-medium">{day}</span>
              <span className="text-muted-foreground">{time}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Key Features */}
      <div>
        <h3 className="font-semibold mb-3">Why Choose Us</h3>
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-primary" />
            <span>Verified business with proven track record</span>
          </div>
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-primary" />
            <span>Quick response time within 24 hours</span>
          </div>
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-primary" />
            <span>Competitive pricing and flexible packages</span>
          </div>
          <div className="flex items-center gap-3">
            <CheckCircle className="w-5 h-5 text-primary" />
            <span>100% satisfaction guarantee</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderGalleryTab = () => (
    <div className="space-y-6">
      {/* Main Image */}
      <div className="aspect-video rounded-2xl overflow-hidden">
        <ImageWithFallback
          src={gallery[selectedImage]}
          alt={`${vendor.name} gallery ${selectedImage + 1}`}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Image Grid */}
      <div className="grid grid-cols-4 gap-3">
        {gallery.map((image, index) => (
          <button
            key={index}
            onClick={() => setSelectedImage(index)}
            className={`aspect-square rounded-lg overflow-hidden border-2 transition-colors ${
              selectedImage === index
                ? "border-primary"
                : "border-transparent hover:border-muted-foreground"
            }`}
          >
            <ImageWithFallback
              src={image}
              alt={`${vendor.name} gallery ${index + 1}`}
              className="w-full h-full object-cover"
            />
          </button>
        ))}
      </div>

      {/* Gallery Info */}
      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Showing {gallery.length} photos • Last updated 2 weeks ago
        </p>
      </div>
    </div>
  );

  const renderReviewsTab = () => (
    <div className="space-y-6">
      {/* Rating Summary */}
      <Card className="p-6 rounded-2xl bg-muted/30">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <span className="text-3xl font-bold">{vendor.rating}</span>
              <div className="flex items-center">
                {Array.from({ length: 5 }, (_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(vendor.rating)
                        ? "fill-chart-1 text-chart-1"
                        : "text-muted-foreground"
                    }`}
                  />
                ))}
              </div>
            </div>
            <p className="text-muted-foreground">
              Based on {vendor.reviews} reviews
            </p>
          </div>
          <Button className="rounded-xl">Write Review</Button>
        </div>
      </Card>

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id} className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {review.customer
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold">{review.customer}</h4>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex items-center">
                        {Array.from({ length: 5 }, (_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${
                              i < review.rating
                                ? "fill-chart-1 text-chart-1"
                                : "text-muted-foreground"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {review.date}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <p className="text-muted-foreground leading-relaxed">
                {review.comment}
              </p>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderContactTab = () => (
    <div className="space-y-6">
      {/* Contact Information */}
      <div className="space-y-4">
        <h3 className="font-semibold">Contact Information</h3>

        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted/50">
            <Phone className="w-5 h-5 text-muted-foreground" />
            <span>+****************</span>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted/50">
            <Mail className="w-5 h-5 text-muted-foreground" />
            <span>
              info@
              {vendor.name.toLowerCase().replace(/\s+/g, "")}
              .com
            </span>
          </div>

          <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted/50">
            <Globe className="w-5 h-5 text-muted-foreground" />
            <span>
              www.
              {vendor.name.toLowerCase().replace(/\s+/g, "")}
              .com
            </span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-3">
        <h3 className="font-semibold">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-3">
          <Button variant="outline" className="rounded-xl">
            <Phone className="w-4 h-4 mr-2" />
            Call Now
          </Button>
          <Button variant="outline" className="rounded-xl">
            <Mail className="w-4 h-4 mr-2" />
            Email
          </Button>
          <Button variant="outline" className="rounded-xl">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule
          </Button>
          <Button variant="outline" className="rounded-xl">
            <MapPin className="w-4 h-4 mr-2" />
            Directions
          </Button>
        </div>
      </div>

      {/* Map Placeholder */}
      <div className="aspect-video bg-muted rounded-2xl flex items-center justify-center">
        <div className="w-full">
          <iframe
            width="100%"
            height="600"
            frameBorder="0"
            scrolling="no"
            marginHeight="0"
            marginWidth="0"
            src="https://maps.google.com/maps?width=100%25&amp;height=600&amp;hl=en&amp;q=1%20Grafton%20Street,%20Dublin,%20Ireland+(My%20Business%20Name)&amp;t=&amp;z=14&amp;ie=UTF8&amp;iwloc=B&amp;output=embed"
          >
            <a href="https://www.mapsdirections.info/fr/calculer-la-population-sur-une-carte">
              mesurer la population sur une carte
            </a>
          </iframe>
        </div>
      </div>
    </div>
  );

  const renderCurrentTab = () => {
    switch (activeTab) {
      case "overview":
        return renderOverviewTab();
      case "gallery":
        return renderGalleryTab();
      case "reviews":
        return renderReviewsTab();
      case "contact":
        return renderContactTab();
      default:
        return renderOverviewTab();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="bg-card rounded-3xl shadow-2xl max-w-6xl w-full max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-border flex-shrink-0">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 rounded-2xl overflow-hidden bg-muted">
                <ImageWithFallback
                  src={vendor.image}
                  alt={vendor.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-2xl font-bold">{vendor.name}</h2>
                  {vendor.isVerified && (
                    <Badge className="bg-primary/10 text-primary">
                      ✓ Verified
                    </Badge>
                  )}
                </div>
                <p className="text-muted-foreground mb-2">{vendor.category}</p>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-chart-1 text-chart-1" />
                    <span className="font-medium">{vendor.rating}</span>
                    <span className="text-sm text-muted-foreground">
                      ({vendor.reviews} reviews)
                    </span>
                  </div>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <MapPin className="w-4 h-4" />
                    {vendor.location}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className="rounded-xl"
              >
                <Heart
                  className={`w-4 h-4 ${
                    isLiked ? "fill-destructive text-destructive" : ""
                  }`}
                />
              </Button>
              <Button variant="ghost" size="sm" className="rounded-xl">
                <Share2 className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="rounded-xl"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex gap-2 mt-6">
            {tabs.map((tab) => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveTab(tab.id)}
                className="rounded-xl"
              >
                {tab.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-1 min-h-0">
          <ScrollArea className="flex-1">
            <div className="p-6">{renderCurrentTab()}</div>
          </ScrollArea>

          {/* Enquiry Sidebar */}
          <div className="w-80 border-l border-border flex-shrink-0">
            <ScrollArea className="h-full">
              <div className="p-6 space-y-6">
                <div>
                  <h3 className="font-semibold mb-2">Send Enquiry</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Get in touch with {vendor.name} for your requirements
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Your Message</label>
                    <Textarea
                      placeholder="Hi, I'm interested in your services. Could you please provide more details about..."
                      value={enquiryMessage}
                      onChange={(e) => setEnquiryMessage(e.target.value)}
                      className="rounded-xl min-h-32"
                    />
                  </div>

                  <Button
                    onClick={handleSendEnquiry}
                    disabled={!enquiryMessage.trim()}
                    className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Send Enquiry
                  </Button>
                </div>

                {/* Quick Info */}
                <Card className="p-4 rounded-2xl bg-muted/30">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Response Time
                      </span>
                      <span className="text-sm font-medium">
                        Within 24 hours
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Availability
                      </span>
                      <Badge className="bg-primary/10 text-primary text-xs">
                        Online
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        Languages
                      </span>
                      <span className="text-sm font-medium">
                        English, Spanish
                      </span>
                    </div>
                  </div>
                </Card>
              </div>
            </ScrollArea>
          </div>
        </div>
      </Card>
    </div>
  );
}
