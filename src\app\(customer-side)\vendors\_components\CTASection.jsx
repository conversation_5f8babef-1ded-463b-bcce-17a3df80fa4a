import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowRight, CheckCircle } from "lucide-react";

export default function CTASection({ onGetStarted, onLogin }) {
  return (
    <section className="px-6 py-16">
      <div className="max-w-4xl mx-auto text-center">
        <Card className="p-12 rounded-2xl border shadow-lg bg-gradient-to-br from-primary/5 to-accent/5">
          <div className="space-y-6">
            <div className="space-y-4">
              <h2 className="text-3xl font-bold">Ready to Grow Your Business?</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Join VendorHub today and start connecting with customers, managing your business, and increasing your revenue.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button onClick={onGetStarted} size="lg" className="rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground h-12 px-8">
                Get Started Now
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
              <Button variant="outline" onClick={onLogin} size="lg" className="rounded-xl h-12 px-8">
                Already a member? Sign In
              </Button>
            </div>

            <div className="flex items-center justify-center gap-6 pt-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-primary" />
                <span>No setup fees</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-primary" />
                <span>30-day free trial</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-primary" />
                <span>Cancel anytime</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
}

