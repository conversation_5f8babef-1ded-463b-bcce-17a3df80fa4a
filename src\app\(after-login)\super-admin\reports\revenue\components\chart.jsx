import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Responsive<PERSON><PERSON>r,
  Legend,
} from "recharts";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Database } from "lucide-react";

const chartConfig = {
  starter: {
    label: "Starter",
    color: "var(--chart-1)",
  },
  pro: {
    label: "Pro",
    color: "var(--chart-2)",
  },
  enterprise: {
    label: "Enterprise",
    color: "var(--chart-3)",
  },
};

const COLORS = ["var(--chart-1)", "var(--chart-2)", "var(--chart-3)", "var(--chart-4)", "var(--chart-5)"];

export function RevenueChart({ chartData, dateFilter }) {
  return (
    <Card className={"my-4"}>
      <CardHeader>
        <CardTitle>Revenue Distribution by Plan</CardTitle>
        <CardDescription>
          Showing revenue breakdown for the {dateFilter.preset}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {chartData?.length > 0 ? (
          <ChartContainer
            config={chartConfig}
            style={{ width: "100%", height: 300 }}
          >
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-background border rounded-lg p-2 shadow-lg">
                          <p className="font-medium">{data.name}</p>
                          <p className="text-sm text-muted-foreground">
                            Revenue: ${data.value?.toLocaleString()}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Count: {data.count} plans
                          </p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </ChartContainer>
        ) : (
          <div className="flex justify-center items-center h-60">
            <div className="flex flex-col items-center justify-center">
              <Database className="w-20 h-20 mx-auto mb-4 opacity-50" />
              <CardTitle>Oops! Data not Found</CardTitle>
              <CardDescription>
                No data found, try changing the date filter.
              </CardDescription>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
