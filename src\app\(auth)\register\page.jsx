"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Building2, ArrowLeft, Upload, MapPin } from "lucide-react";
import Link from "next/link";

export default function RegisterScreen() {
  const router = useRouter();

  const [selectedRole, setSelectedRole] = useState("customer");
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    businessName: "",
    category: "",
    location: "",
    region: "",
    assignedAdmin: "",
    agreeToTerms: false,
  });
  const [isLoading, setIsLoading] = useState(false);

  const updateFormData = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const getMaxSteps = () => {
    switch (selectedRole) {
      case "customer":
        return 1;
      case "vendor":
        return 3;
      case "sales_executive":
        return 2;
      default:
        return 1;
    }
  };

  const handleNext = () => {
    if (selectedRole === "customer" || currentStep === getMaxSteps()) {
      handleSubmit();
    } else {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handleSubmit = () => {
    if (!formData.agreeToTerms) return;

    setIsLoading(true);

    setTimeout(() => {
      setIsLoading(false);

      // Optional: simulate onRegister callback
      console.log("User registered:", { ...formData, role: selectedRole });

      // Redirect based on role
      const roleRedirects = {
        customer: "/customer/home",
        vendor: "/vendor/dashboard",
      };

      const redirectTo = roleRedirects[selectedRole] || "/";
      router.push(redirectTo);
    }, 1000);
  };

  const roleOptions = [
    { value: "customer", label: "Customer" },
    { value: "vendor", label: "Vendor" },
  ];

  const categories = [
    "Restaurants & Food",
    "Retail & Shopping",
    "Services",
    "Healthcare",
    "Technology",
    "Construction",
    "Other",
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <div className="flex-1 text-center">
            <h1 className="text-2xl font-bold text-foreground">
              Create Account
            </h1>
            <p className="text-muted-foreground">Join VendorHub today</p>
          </div>
        </div>

        {/* Progress Steps */}
        {selectedRole !== "customer" && (
          <div className="flex justify-center gap-2">
            {Array.from({ length: getMaxSteps() }, (_, i) => (
              <div
                key={i}
                className={`h-2 w-8 rounded-full ${
                  i + 1 <= currentStep ? "bg-primary" : "bg-muted"
                }`}
              />
            ))}
          </div>
        )}

        {/* Registration Form */}
        <Card className="p-6 rounded-2xl border shadow-lg">
          <div className="space-y-6">
            {/* Step 1: Basic Info */}
            {currentStep === 1 && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="role">I want to join as a</Label>
                  <Select
                    value={selectedRole}
                    onValueChange={(value) => {
                      setSelectedRole(value);
                      setCurrentStep(1);
                    }}
                  >
                    <SelectTrigger className="rounded-xl h-12 w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {roleOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">
                      {selectedRole === "vendor"
                        ? "Contact Person Name"
                        : "Full Name"}
                    </Label>
                    <Input
                      id="name"
                      placeholder="John Doe"
                      value={formData.name}
                      onChange={(e) => updateFormData("name", e.target.value)}
                      className="rounded-xl h-12"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={(e) => updateFormData("email", e.target.value)}
                      className="rounded-xl h-12"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="+****************"
                      value={formData.phone}
                      onChange={(e) => updateFormData("phone", e.target.value)}
                      className="rounded-xl h-12"
                    />
                  </div>
                </div>
              </>
            )}

            {/* Step 2: Business or Role Specific Info */}
            {currentStep === 2 && (
              <>
                {selectedRole === "vendor" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessName">Business Name</Label>
                      <Input
                        id="businessName"
                        placeholder="Your Business Name"
                        value={formData.businessName}
                        onChange={(e) =>
                          updateFormData("businessName", e.target.value)
                        }
                        className="rounded-xl h-12"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="category">Business Category</Label>
                      <Select
                        value={formData.category}
                        onValueChange={(value) =>
                          updateFormData("category", value)
                        }
                      >
                        <SelectTrigger className="rounded-xl h-12">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((cat) => (
                            <SelectItem key={cat} value={cat}>
                              {cat}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="location">Business Location</Label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                        <Input
                          id="location"
                          placeholder="City, State"
                          value={formData.location}
                          onChange={(e) =>
                            updateFormData("location", e.target.value)
                          }
                          className="rounded-xl h-12 pl-10"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {selectedRole === "sales_executive" && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="region">Assigned Region</Label>
                      <Input
                        id="region"
                        placeholder="North District, City Name"
                        value={formData.region}
                        onChange={(e) =>
                          updateFormData("region", e.target.value)
                        }
                        className="rounded-xl h-12"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="assignedAdmin">Reporting Admin</Label>
                      <Select
                        value={formData.assignedAdmin}
                        onValueChange={(value) =>
                          updateFormData("assignedAdmin", value)
                        }
                      >
                        <SelectTrigger className="rounded-xl h-12">
                          <SelectValue placeholder="Select admin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="admin1">
                            John Smith (Admin)
                          </SelectItem>
                          <SelectItem value="admin2">
                            Sarah Johnson (Admin)
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Step 3: Vendor Only - Upload and Payment */}
            {currentStep === 3 && selectedRole === "vendor" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Business Documents</Label>
                  <div className="border-2 border-dashed border-muted-foreground/20 rounded-xl p-6 text-center">
                    <Upload className="mx-auto w-8 h-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground mb-2">
                      Upload business license, certificates
                    </p>
                    <Button variant="outline" size="sm" className="rounded-lg">
                      Choose Files
                    </Button>
                  </div>
                </div>

                <div className="bg-accent/10 rounded-xl p-4">
                  <h3 className="font-semibold text-accent mb-2">
                    Joining Fee
                  </h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    One-time registration fee to get listed on our platform
                  </p>
                  <div className="text-2xl font-bold text-accent">$99</div>
                </div>
              </div>
            )}

            {/* Terms & Conditions */}
            <div className="flex items-start gap-3">
              <Checkbox
                id="terms"
                checked={formData.agreeToTerms}
                onCheckedChange={(checked) =>
                  updateFormData("agreeToTerms", checked)
                }
              />
              <Label htmlFor="terms" className="text-sm leading-relaxed">
                I agree to the{" "}
                <button className="text-primary hover:underline">
                  Terms of Service
                </button>{" "}
                and{" "}
                <button className="text-primary hover:underline">
                  Privacy Policy
                </button>
              </Label>
            </div>

            {/* Navigation Buttons */}
            <div className="flex gap-3">
              {currentStep > 1 && (
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep((prev) => prev - 1)}
                  className="flex-1 h-12 rounded-xl"
                >
                  Back
                </Button>
              )}
              <Button
                onClick={handleNext}
                disabled={!formData.agreeToTerms || isLoading}
                className="flex-1 h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
              >
                {isLoading
                  ? "Creating..."
                  : currentStep === getMaxSteps()
                  ? "Create Account"
                  : "Next"}
              </Button>
            </div>

            {/* Login Link */}
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link
                  href={"/login"}
                  className="text-primary hover:underline font-medium"
                >
                  Login here.
                </Link>
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
