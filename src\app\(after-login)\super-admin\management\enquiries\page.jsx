import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import React from "react";
const recentEnquiries = [
  {
    id: 1,
    customer: "<PERSON> Johnson",
    vendor: "Giuseppe's Kitchen",
    message: "Looking for catering for corporate event",
    date: "2 hours ago",
    status: "new",
  },
  {
    id: 2,
    customer: "<PERSON>",
    vendor: "TechFix Solutions",
    message: "Need laptop repair services",
    date: "4 hours ago",
    status: "responded",
  },
];

export default function EnquiriesManagementPage() {
  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      <h1 className="text-2xl font-bold">Enquiries & Reviews</h1>

      <div className="space-y-4">
        {recentEnquiries.map((enquiry) => (
          <Card key={enquiry.id} className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {enquiry.customer
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{enquiry.customer}</h3>
                    <p className="text-sm text-muted-foreground">
                      to {enquiry.vendor}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {enquiry.date}
                    </p>
                  </div>
                </div>
                <Badge
                  variant={enquiry.status === "new" ? "default" : "secondary"}
                  className={
                    enquiry.status === "new"
                      ? "bg-accent text-white"
                      : "bg-blue-100 text-blue-800"
                  }
                >
                  {enquiry.status}
                </Badge>
              </div>

              <p className="text-foreground">{enquiry.message}</p>

              <div className="flex gap-2">
                <Button size="sm" className="rounded-lg">
                  View Details
                </Button>
                <Button variant="outline" size="sm" className="rounded-lg">
                  Moderate
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
