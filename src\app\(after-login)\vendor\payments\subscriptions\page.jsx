"use client";

import { useMemo, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PLANS, vendorSubscriptions } from "@/constants/plans";

export default function VendorSubscriptionsPage() {
  const [sub, setSub] = useState(vendorSubscriptions.current);
  const [history, setHistory] = useState(vendorSubscriptions.history);
  const current = useMemo(
    () => PLANS.find((p) => p.id === sub.planId),
    [sub.planId]
  );

  const [dialog, setDialog] = useState(null); // 'change' | 'cancel'
  const [selectedPlan, setSelectedPlan] = useState(String(sub.planId));

  const confirmChange = () => {
    const next = PLANS.find((p) => String(p.id) === selectedPlan);
    if (next) setSub((s) => ({ ...s, planId: next.id }));
    setDialog(null);
  };
  const confirmCancel = () => {
    setSub((s) => ({ ...s, status: "canceled" }));
    setDialog(null);
  };

  return (
    <div className="max-w-5xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Subscription</h1>

      <Card className="p-6 rounded-2xl">
        <div className="flex items-start justify-between gap-4">
          <div>
            <div className="text-sm text-muted-foreground">Current Plan</div>
            <div className="text-2xl font-semibold">{current?.name}</div>
            <div className="mt-1 text-sm text-muted-foreground">
              Renews on {sub.renewsOn}
            </div>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">
              ${current?.price}
              <span className="text-base font-medium text-muted-foreground">
                /mo
              </span>
            </div>
            <Badge className="capitalize mt-2">{sub.status}</Badge>
          </div>
        </div>
        <div className="mt-6 flex gap-2">
          <Button
            onClick={() => {
              setSelectedPlan(String(sub.planId));
              setDialog("change");
            }}
          >
            Change Plan
          </Button>
          <Button variant="outline" onClick={() => setDialog("cancel")}>
            Cancel
          </Button>
        </div>
      </Card>

      <Card className="p-6 rounded-2xl">
        <h3 className="text-lg font-semibold mb-4">Billing History</h3>
        <div className="space-y-3">
          {history.map((h) => (
            <div
              key={h.id}
              className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
            >
              <div>
                <div className="font-medium">{h.plan}</div>
                <div className="text-sm text-muted-foreground">{h.date}</div>
              </div>
              <div className="flex items-center gap-3">
                <div className="font-semibold">${h.amount}</div>
                <Badge className="capitalize">{h.status}</Badge>
                <Button size="sm" variant="outline">
                  Download
                </Button>
              </div>
            </div>
          ))}
        </div>
      </Card>

      <Dialog
        open={dialog === "change"}
        onOpenChange={(o) => !o && setDialog(null)}
      >
        {dialog === "change" && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Change Plan</DialogTitle>
            </DialogHeader>
            <div>
              <label className="text-sm">Select plan</label>
              <Select value={selectedPlan} onValueChange={setSelectedPlan}>
                <SelectTrigger className="mt-1 rounded-xl">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {PLANS.map((p) => (
                    <SelectItem key={p.id} value={String(p.id)}>
                      {p.name} - ${p.price}/mo
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDialog(null)}>
                Cancel
              </Button>
              <Button onClick={confirmChange}>Confirm</Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>

      <Dialog
        open={dialog === "cancel"}
        onOpenChange={(o) => !o && setDialog(null)}
      >
        {dialog === "cancel" && (
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Cancel subscription?</DialogTitle>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setDialog(null)}>
                No
              </Button>
              <Button variant="destructive" onClick={confirmCancel}>
                Yes, cancel
              </Button>
            </DialogFooter>
          </DialogContent>
        )}
      </Dialog>
    </div>
  );
}
