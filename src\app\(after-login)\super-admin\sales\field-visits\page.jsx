import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { upcomingVisits, completedVisits } from "@/constants/sales";

export default function SalesFieldVisitsPage() {
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold">Field Visits</h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Upcoming</h3>
          <div className="space-y-3">
            {upcomingVisits.map((v) => (
              <div
                key={v.id}
                className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
              >
                <div>
                  <div className="font-medium">{v.purpose}</div>
                  <div className="text-sm text-muted-foreground">
                    {v.location} • {v.date} {v.time}
                  </div>
                </div>
                <Badge className="capitalize">{v.status}</Badge>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Completed</h3>
          <div className="space-y-3">
            {completedVisits.map((v) => (
              <div key={v.id} className="p-3 bg-muted/30 rounded-xl">
                <div className="flex items-center justify-between">
                  <div className="font-medium">{v.purpose}</div>
                  <Badge variant="outline">{v.date}</Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  {v.location} • {v.time}
                </div>
                <div className="mt-2 text-sm">Outcome: {v.outcome}</div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}
