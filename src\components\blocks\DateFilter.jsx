import React from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RotateCcw } from "lucide-react";
import DatePicker from "@/components/ui/date-picker";

export default function DateFilter({ dateFilter, onFilterChange }) {
  const handlePresetChange = (preset) => {
    const now = new Date();
    let startDate = "";
    let endDate = "";

    switch (preset) {
      case "today":
        startDate = now.toISOString().split("T")[0];
        endDate = now.toISOString().split("T")[0];
        break;
      case "week":
        const weekAgo = new Date(now);
        weekAgo.setDate(now.getDate() - 7);
        startDate = weekAgo.toISOString().split("T")[0];
        endDate = now.toISOString().split("T")[0];
        break;
      case "month":
        const monthAgo = new Date(now);
        monthAgo.setMonth(now.getMonth() - 1);
        startDate = monthAgo.toISOString().split("T")[0];
        endDate = now.toISOString().split("T")[0];
        break;
      case "3months":
        const threeMonthsAgo = new Date(now);
        threeMonthsAgo.setMonth(now.getMonth() - 3);
        startDate = threeMonthsAgo.toISOString().split("T")[0];
        endDate = now.toISOString().split("T")[0];
        break;
      case "6months":
        const sixMonthsAgo = new Date(now);
        sixMonthsAgo.setMonth(now.getMonth() - 6);
        startDate = sixMonthsAgo.toISOString().split("T")[0];
        endDate = now.toISOString().split("T")[0];
        break;
      case "year":
        const yearAgo = new Date(now);
        yearAgo.setFullYear(now.getFullYear() - 1);
        startDate = yearAgo.toISOString().split("T")[0];
        endDate = now.toISOString().split("T")[0];
        break;
      case "custom":
        // Keep existing dates for custom
        startDate = dateFilter.startDate;
        endDate = dateFilter.endDate;
        break;
      default:
        break;
    }

    onFilterChange({
      ...dateFilter,
      preset,
      startDate,
      endDate,
    });
  };

  const handleDateChange = (field, value) => {
    onFilterChange({
      ...dateFilter,
      [field]: value,
      preset: "custom",
    });
  };

  const handleReset = () => {
    onFilterChange({
      startDate: "",
      endDate: "",
      preset: "week",
    });
  };

  return (
    <>
      <div className="flex gap-4 items-end">
        {/* Preset Selector */}
        <Select value={dateFilter?.preset} onValueChange={handlePresetChange}>
          <SelectTrigger>
            <SelectValue placeholder="Select time period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">Last 7 Days</SelectItem>
            <SelectItem value="month">Last Month</SelectItem>
            <SelectItem value="3months">Last 3 Months</SelectItem>
            <SelectItem value="6months">Last 6 Months</SelectItem>
            <SelectItem value="year">Last Year</SelectItem>
            <SelectItem value="custom">Custom Range</SelectItem>
          </SelectContent>
        </Select>

        {dateFilter?.preset === "custom" && (
          <div className="flex gap-4 items-center">
            {/* Start Date */}
            <div className="flex-1 grid gap-2 min-w-[150px]">
              <Label htmlFor="startDate">Start Date</Label>
              <DatePicker
                id="startDate"
                date={dateFilter?.startDate}
                setDate={(value) => handleDateChange("startDate", value)}
                placeholder="Select Start Date"
              />
            </div>

            {/* End Date */}
            <div className="flex-1 grid gap-2 min-w-[150px]">
              <Label htmlFor="endDate">End Date</Label>
              <DatePicker
                id="endDate"
                date={dateFilter?.endDate}
                setDate={(value) => handleDateChange("endDate", value)}
                placeholder="Select End Date"
              />
            </div>
          </div>
        )}
        {/* Reset Button */}
        <Button
          variant="outline"
          size="icon"
          onClick={handleReset}
          title="Reset to default (Last 7 days)"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>
    </>
  );
}
