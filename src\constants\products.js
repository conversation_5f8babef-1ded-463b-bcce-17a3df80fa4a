// E-commerce Products Data
export const products = [
  // Electronics
  {
    id: 1,
    name: "iPhone 15 Pro Max",
    slug: "iphone-15-pro-max",
    category: { id: 1, name: "Electronics", slug: "electronics" },
    subcategory: "Smartphones",
    brand: "Apple",
    price: 1199.99,
    originalPrice: 1299.99,
    discount: 8,
    rating: 4.8,
    reviewCount: 2847,
    inStock: true,
    stockCount: 45,
    images: [
      "https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=500",
      "https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=500",
      "https://images.unsplash.com/photo-1695048133142-1a20484d2569?w=500"
    ],
    description: "The most advanced iPhone ever with titanium design, A17 Pro chip, and professional camera system.",
    features: ["6.7-inch Super Retina XDR display", "A17 Pro chip", "Pro camera system", "Titanium design"],
    specifications: {
      "Display": "6.7-inch Super Retina XDR",
      "Chip": "A17 Pro",
      "Storage": "256GB",
      "Camera": "48MP Main, 12MP Ultra Wide, 12MP Telephoto",
      "Battery": "Up to 29 hours video playback"
    },
    tags: ["smartphone", "apple", "pro", "camera", "titanium"],
    isFeatured: true,
    isNew: true,
    isBestSeller: true
  },
  {
    id: 2,
    name: "MacBook Air M3",
    slug: "macbook-air-m3",
    category: { id: 1, name: "Electronics", slug: "electronics" },
    subcategory: "Laptops",
    brand: "Apple",
    price: 1099.99,
    originalPrice: 1199.99,
    discount: 8,
    rating: 4.9,
    reviewCount: 1523,
    inStock: true,
    stockCount: 23,
    images: [
      "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500",
      "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500"
    ],
    description: "Supercharged by the M3 chip, MacBook Air is up to 60% faster than the previous generation.",
    features: ["M3 chip", "13.6-inch Liquid Retina display", "Up to 18 hours battery", "1080p FaceTime HD camera"],
    specifications: {
      "Chip": "Apple M3",
      "Display": "13.6-inch Liquid Retina",
      "Memory": "8GB unified memory",
      "Storage": "256GB SSD",
      "Battery": "Up to 18 hours"
    },
    tags: ["laptop", "apple", "m3", "portable", "productivity"],
    isFeatured: true,
    isNew: false,
    isBestSeller: true
  },
  {
    id: 3,
    name: "Samsung Galaxy S24 Ultra",
    slug: "samsung-galaxy-s24-ultra",
    category: { id: 1, name: "Electronics", slug: "electronics" },
    subcategory: "Smartphones",
    brand: "Samsung",
    price: 1199.99,
    originalPrice: 1299.99,
    discount: 8,
    rating: 4.7,
    reviewCount: 1892,
    inStock: true,
    stockCount: 67,
    images: [
      "https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500",
      "https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500"
    ],
    description: "The ultimate Galaxy experience with S Pen, advanced AI features, and pro-grade camera.",
    features: ["6.8-inch Dynamic AMOLED 2X", "S Pen included", "200MP camera", "5000mAh battery"],
    specifications: {
      "Display": "6.8-inch Dynamic AMOLED 2X",
      "Processor": "Snapdragon 8 Gen 3",
      "Storage": "256GB",
      "Camera": "200MP Main, 50MP Periscope Telephoto",
      "Battery": "5000mAh"
    },
    tags: ["smartphone", "samsung", "s-pen", "camera", "android"],
    isFeatured: true,
    isNew: true,
    isBestSeller: false
  },

  // Fashion
  {
    id: 4,
    name: "Nike Air Max 270",
    slug: "nike-air-max-270",
    category: { id: 2, name: "Fashion", slug: "fashion" },
    subcategory: "Shoes",
    brand: "Nike",
    price: 150.00,
    originalPrice: 180.00,
    discount: 17,
    rating: 4.6,
    reviewCount: 3421,
    inStock: true,
    stockCount: 89,
    images: [
      "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500",
      "https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500"
    ],
    description: "Inspired by two icons of big Air: the Air Max 180 and Air Max 93.",
    features: ["Max Air unit", "Engineered mesh upper", "Foam midsole", "Rubber outsole"],
    specifications: {
      "Upper": "Engineered mesh and synthetic leather",
      "Midsole": "Foam with Max Air unit",
      "Outsole": "Rubber with waffle pattern",
      "Fit": "True to size"
    },
    tags: ["shoes", "nike", "air-max", "sneakers", "running"],
    sizes: ["7", "8", "9", "10", "11", "12"],
    colors: ["Black/White", "White/Blue", "Red/Black"],
    isFeatured: false,
    isNew: false,
    isBestSeller: true
  },
  {
    id: 5,
    name: "Levi's 501 Original Jeans",
    slug: "levis-501-original-jeans",
    category: { id: 2, name: "Fashion", slug: "fashion" },
    subcategory: "Clothing",
    brand: "Levi's",
    price: 89.99,
    originalPrice: 109.99,
    discount: 18,
    rating: 4.5,
    reviewCount: 2156,
    inStock: true,
    stockCount: 156,
    images: [
      "https://images.unsplash.com/photo-1542272604-787c3835535d?w=500",
      "https://images.unsplash.com/photo-1542272604-787c3835535d?w=500"
    ],
    description: "The original blue jean since 1873. A blank canvas for self-expression.",
    features: ["100% cotton denim", "Button fly", "Straight leg", "Classic 5-pocket styling"],
    specifications: {
      "Material": "100% Cotton",
      "Fit": "Straight",
      "Rise": "Mid-rise",
      "Closure": "Button fly"
    },
    tags: ["jeans", "levis", "denim", "classic", "straight"],
    sizes: ["28", "30", "32", "34", "36", "38"],
    colors: ["Dark Blue", "Light Blue", "Black"],
    isFeatured: false,
    isNew: false,
    isBestSeller: true
  }
];

// Featured products for home page
export const featuredProducts = products.filter(p => p.isFeatured);

// Best sellers
export const bestSellerProducts = products.filter(p => p.isBestSeller);

// New arrivals
export const newProducts = products.filter(p => p.isNew);

// Deal of the day
export const dealOfTheDay = {
  id: 1,
  product: products[0],
  originalPrice: 1299.99,
  salePrice: 999.99,
  discount: 23,
  endsAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
  soldCount: 127,
  totalStock: 200
};

// Brands
export const brands = [
  { id: 1, name: "Apple", logo: "https://cdn-icons-png.flaticon.com/128/179/179309.png", productCount: 45 },
  { id: 2, name: "Samsung", logo: "https://cdn-icons-png.flaticon.com/128/5968/5968841.png", productCount: 67 },
  { id: 3, name: "Nike", logo: "https://cdn-icons-png.flaticon.com/128/732/732084.png", productCount: 89 },
  { id: 4, name: "Levi's", logo: "https://cdn-icons-png.flaticon.com/128/732/732190.png", productCount: 34 },
  { id: 5, name: "Sony", logo: "https://cdn-icons-png.flaticon.com/128/5968/5968830.png", productCount: 56 }
];

// Price ranges for filters
export const priceRanges = [
  { id: 1, label: "Under $50", min: 0, max: 50 },
  { id: 2, label: "$50 - $100", min: 50, max: 100 },
  { id: 3, label: "$100 - $250", min: 100, max: 250 },
  { id: 4, label: "$250 - $500", min: 250, max: 500 },
  { id: 5, label: "$500 - $1000", min: 500, max: 1000 },
  { id: 6, label: "Over $1000", min: 1000, max: Infinity }
];

// Rating filters
export const ratingFilters = [
  { id: 1, rating: 4, label: "4 stars & up" },
  { id: 2, rating: 3, label: "3 stars & up" },
  { id: 3, rating: 2, label: "2 stars & up" },
  { id: 4, rating: 1, label: "1 star & up" }
];
