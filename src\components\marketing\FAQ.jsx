import { Card } from "@/components/ui/card";

export default function FAQ({ faqs }) {
  return (
    <section id="faq" className="py-24 bg-muted/30">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-3xl md:text-4xl font-bold">Frequently Asked Questions</h2>
          <p className="text-xl text-muted-foreground">Find answers to common questions about VendorHub</p>
        </div>
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <Card key={index} className="p-6 rounded-2xl border-0 shadow-sm">
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">{faq.question}</h3>
                <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

