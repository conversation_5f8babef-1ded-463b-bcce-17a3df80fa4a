"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { SlidersHorizontal, X, Star } from "lucide-react";

export function FilterPanel({
  categories = [],
  brands = [],
  priceRange = [0, 2000],
  ratingFilters = [],
  selectedCategories = [],
  selectedBrands = [],
  selectedPriceRange = [0, 2000],
  selectedRating = 0,
  onCategoryChange,
  onBrandChange,
  onPriceRangeChange,
  onRatingChange,
  onClearFilters,
  className = ""
}) {
  const hasActiveFilters = 
    selectedCategories.length > 0 || 
    selectedBrands.length > 0 || 
    selectedRating > 0 ||
    selectedPriceRange[0] !== priceRange[0] ||
    selectedPriceRange[1] !== priceRange[1];

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-lg">Filters</h3>
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={onClearFilters}>
              <SlidersHorizontal className="w-4 h-4 mr-2" />
              Clear
            </Button>
          )}
        </div>
        
        <div className="space-y-6">
          {/* Categories */}
          {categories.length > 0 && (
            <FilterSection title="Categories">
              <div className="space-y-2">
                {categories.map(category => (
                  <div key={category.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={selectedCategories.includes(category.id)}
                      onCheckedChange={(checked) => onCategoryChange(category.id, checked)}
                    />
                    <label 
                      htmlFor={`category-${category.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                    >
                      {category.name} ({category.count})
                    </label>
                  </div>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Price Range */}
          <FilterSection title="Price Range">
            <div className="space-y-4">
              <Slider
                value={selectedPriceRange}
                onValueChange={onPriceRangeChange}
                max={priceRange[1]}
                min={priceRange[0]}
                step={10}
                className="w-full"
              />
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>${selectedPriceRange[0]}</span>
                <span>${selectedPriceRange[1]}</span>
              </div>
            </div>
          </FilterSection>

          {/* Brands */}
          {brands.length > 0 && (
            <FilterSection title="Brands">
              <div className="space-y-2">
                {brands.map(brand => (
                  <div key={brand.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`brand-${brand.id}`}
                      checked={selectedBrands.includes(brand.name)}
                      onCheckedChange={(checked) => onBrandChange(brand.name, checked)}
                    />
                    <label 
                      htmlFor={`brand-${brand.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                    >
                      {brand.name} ({brand.productCount})
                    </label>
                  </div>
                ))}
              </div>
            </FilterSection>
          )}

          {/* Rating */}
          {ratingFilters.length > 0 && (
            <FilterSection title="Customer Rating">
              <div className="space-y-2">
                {ratingFilters.map(filter => (
                  <div key={filter.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`rating-${filter.id}`}
                      checked={selectedRating === filter.rating}
                      onCheckedChange={(checked) => onRatingChange(checked ? filter.rating : 0)}
                    />
                    <label 
                      htmlFor={`rating-${filter.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex items-center"
                    >
                      <div className="flex items-center mr-2">
                        {[...Array(5)].map((_, i) => (
                          <Star 
                            key={i} 
                            className={`w-4 h-4 ${i < filter.rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} 
                          />
                        ))}
                      </div>
                      {filter.label}
                    </label>
                  </div>
                ))}
              </div>
            </FilterSection>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Filter section wrapper
function FilterSection({ title, children }) {
  return (
    <div>
      <h4 className="font-medium mb-3">{title}</h4>
      {children}
      <Separator className="mt-4" />
    </div>
  );
}

// Active filters display
export function ActiveFilters({
  categories = [],
  selectedCategories = [],
  selectedBrands = [],
  selectedRating = 0,
  onCategoryRemove,
  onBrandRemove,
  onRatingRemove,
  onClearAll,
  className = ""
}) {
  const hasFilters = selectedCategories.length > 0 || selectedBrands.length > 0 || selectedRating > 0;

  if (!hasFilters) return null;

  return (
    <div className={`flex items-center gap-2 flex-wrap ${className}`}>
      <span className="text-sm text-gray-500">Filters:</span>
      
      {selectedCategories.map(catId => {
        const category = categories.find(c => c.id === catId);
        return (
          <Badge key={catId} variant="secondary" className="gap-1">
            {category?.name}
            <X 
              className="w-3 h-3 cursor-pointer" 
              onClick={() => onCategoryRemove(catId)}
            />
          </Badge>
        );
      })}
      
      {selectedBrands.map(brand => (
        <Badge key={brand} variant="secondary" className="gap-1">
          {brand}
          <X 
            className="w-3 h-3 cursor-pointer" 
            onClick={() => onBrandRemove(brand)}
          />
        </Badge>
      ))}
      
      {selectedRating > 0 && (
        <Badge variant="secondary" className="gap-1">
          {selectedRating}+ stars
          <X 
            className="w-3 h-3 cursor-pointer" 
            onClick={() => onRatingRemove()}
          />
        </Badge>
      )}
      
      <Button variant="ghost" size="sm" onClick={onClearAll}>
        Clear all
      </Button>
    </div>
  );
}

export default FilterPanel;
