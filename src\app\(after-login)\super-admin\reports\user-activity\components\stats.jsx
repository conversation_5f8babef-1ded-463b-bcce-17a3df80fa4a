import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Activity, LogIn, AlertTriangle } from "lucide-react";

export default function StatsCard({ activities, setFilter }) {
  const [stats, setStats] = useState({
    totalActivities: 0,
    successfulLogins: 0,
    failedActions: 0,
    uniqueUsers: 0,
  });

  useEffect(() => {
    const successfulLogins = activities.filter((a) => a.actionType === "login" && a.success);
    const failedActions = activities.filter((a) => !a.success);
    const uniqueUsers = new Set(activities.map((a) => a.userName)).size;
    
    setStats({
      totalActivities: activities?.length || 0,
      successfulLogins: successfulLogins?.length || 0,
      failedActions: failedActions?.length || 0,
      uniqueUsers: uniqueUsers,
    });
  }, [activities]);

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Activity className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalActivities?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Activities</div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("login")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <LogIn className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.successfulLogins.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Successful Logins
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("failed")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <AlertTriangle className="w-5 h-5 text-red-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.failedActions?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Failed Actions
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors shimmer duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Activity className="w-5 h-5 text-blue-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.uniqueUsers?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Unique Users
          </div>
        </div>
      </Card>
    </div>
  );
}
