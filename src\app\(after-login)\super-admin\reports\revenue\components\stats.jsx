import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { DollarSign, TrendingUp, CreditCard, Banknote } from "lucide-react";

export default function StatsCard({ revenues, setFilter }) {
  const [stats, setStats] = useState({
    totalRevenue: 0,
    totalCommission: 0,
    totalNetRevenue: 0,
    averageAmount: 0,
  });

  useEffect(() => {
    const totalRevenue = revenues.reduce((sum, r) => sum + r.amount, 0);
    const totalCommission = revenues.reduce((sum, r) => sum + r.commission, 0);
    const totalNetRevenue = revenues.reduce((sum, r) => sum + r.netRevenue, 0);
    const averageAmount = revenues.length > 0 ? totalRevenue / revenues.length : 0;
    
    setStats({
      totalRevenue,
      totalCommission,
      totalNetRevenue,
      averageAmount,
    });
  }, [revenues]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <DollarSign className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.totalRevenue)}
          </div>
          <div className="text-sm text-muted-foreground">Total Revenue</div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("commission")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <TrendingUp className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.totalCommission)}
          </div>
          <div className="text-sm text-muted-foreground">
            Total Commission
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("net")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Banknote className="w-5 h-5 text-blue-500" />
          </div>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.totalNetRevenue)}
          </div>
          <div className="text-sm text-muted-foreground">
            Net Revenue
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors shimmer duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CreditCard className="w-5 h-5 text-orange-500" />
          </div>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.averageAmount)}
          </div>
          <div className="text-sm text-muted-foreground">
            Average Amount
          </div>
        </div>
      </Card>
    </div>
  );
}
