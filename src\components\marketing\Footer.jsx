import { Building2 } from "lucide-react";

export default function Footer() {
  return (
    <footer className="bg-muted py-16 border-t">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary rounded-2xl flex items-center justify-center">
                <Building2 className="w-5 h-5 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold">Jo <PERSON><PERSON></span>
            </div>
            <p className="text-muted-foreground">
              The ultimate platform for vendors, customers, and sales teams to
              grow their business.
            </p>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold">Product</h4>
            <div className="space-y-2 text-muted-foreground">
              <div>Features</div>
              <div>Pricing</div>
              <div>API</div>
              <div>Documentation</div>
            </div>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold">Company</h4>
            <div className="space-y-2 text-muted-foreground">
              <div>About Us</div>
              <div>Careers</div>
              <div>Blog</div>
              <div>Press</div>
            </div>
          </div>
          <div className="space-y-4">
            <h4 className="font-semibold">Support</h4>
            <div className="space-y-2 text-muted-foreground">
              <div>Help Center</div>
              <div>Contact Us</div>
              <div>Privacy Policy</div>
              <div>Terms of Service</div>
            </div>
          </div>
        </div>
        <div className="border-t border-border pt-8 text-center text-muted-foreground">
          <p>&copy; 2025 VendorHub. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
