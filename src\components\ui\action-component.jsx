"use client";
import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreVertical } from "lucide-react";
import { toast } from "sonner";

export default function ActionComponent() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <MoreVertical size={18} />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className={"text-muted-foreground"}>
          Actions
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem>Edit</DropdownMenuItem>
        <DropdownMenuItem
          onClick={async () => {
            const confirmation = confirm(
              "Are you sure you want to delete this entry?"
            );
            if (confirmation) {
              try {
                toast.success("Successfully deleted the Entry.");
              } catch (error) {
                toast?.error(
                  "Error occured while deleting, please try again later..."
                );
              }
            }
          }}
        >
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
