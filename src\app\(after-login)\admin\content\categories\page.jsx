"use client";

import { useMemo, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Edit, Plus, Search } from "lucide-react";
import { categories } from "@/constants/categories";
import { DataTable } from "@/components/ui/data-table";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import ActionComponent from "@/components/ui/action-component";
import { Label } from "@/components/ui/label";

export default function CategoriesPage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editing, setEditing] = useState(null);
  const [form, setForm] = useState({
    name: "",
    description: "",
    icon: null,
    isActive: true,
  });

  const openAdd = () => {
    setEditing(null);
    setForm({ name: "", isActive: true });
    setDialogOpen(true);
  };

  const columns = [
    {
      id: "id",
      accessorKey: "name",
      header: "Name",
      filterable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <ImageWithFallback
            src={row.original?.icon}
            className="w-12 rounded-md mr-2"
          />
          <p>{row.original?.name}</p>
        </div>
      ),
    },
    {
      id: "isActive",
      accessorKey: "isActive",
      header: "Active",
      filterable: true,
      cell: ({ row }) => (
        <Badge variant={row.original?.isActive ? "default" : "secondary"}>
          {row.original?.isActive ? "Active" : "Inactive"}
        </Badge>
      ),
    },
    {
      id: "slug",
      accessorKey: "slug",
      header: "Slug",
      filterable: true,
      cell: ({ row }) => <div>{row.original?.slug}</div>,
    },
    {
      id: "description",
      accessorKey: "description",
      header: "Description",
      filterable: true,
      cell: ({ row }) => <div>{row.original?.description}</div>,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => <ActionComponent />,
    },
  ];

  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between gap-4">
        <h1 className="text-2xl font-bold">Category Management</h1>
        <div className="flex items-center gap-3 w-full sm:w-auto">
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button
                onClick={openAdd}
                className="rounded-xl bg-primary hover:bg-primary/90"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editing ? "Edit Category" : "Add Category"}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <div>
                    <Label className="text-sm font-medium">Name</Label>
                    <Input
                      value={form.name}
                      onChange={(e) =>
                        setForm((f) => ({ ...f, name: e.target.value }))
                      }
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <Input
                      value={form.description}
                      onChange={(e) =>
                        setForm((f) => ({ ...f, description: e.target.value }))
                      }
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Icon</Label>
                    <Input
                      type={"file"}
                      value={form.icon}
                      onChange={(e) =>
                        setForm((f) => ({ ...f, icon: e.target.value }))
                      }
                      className="mt-1"
                    />
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Active</span>
                  <Switch
                    checked={form.isActive}
                    onCheckedChange={(v) =>
                      setForm((f) => ({ ...f, isActive: v }))
                    }
                  />
                </div>
              </div>
              <DialogFooter>
                <Button onClick={() => setDialogOpen(false)} variant="outline">
                  Cancel
                </Button>
                <Button>{editing ? "Save" : "Create"}</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Data Table */}
      <DataTable columns={columns} data={categories} />
    </div>
  );
}
