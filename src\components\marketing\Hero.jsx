"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { ArrowRight, CheckCircle, TrendingUp } from "lucide-react";

export default function Hero({ onGetStarted, onLearnMore, stats }) {
  return (
    <section
      id="home"
      className="pt-6 pb-12 bg-gradient-to-br from-primary/5 via-background to-accent/5"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge className="bg-primary/10 text-primary border-primary/20">
                🚀 Trusted by 10,000+ businesses
              </Badge>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                Connect with{" "}
                <span className="text-primary">Trusted Vendors</span>{" "}
                Effortlessly
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                The ultimate platform for vendors, customers, and sales teams to
                grow their business. Join thousands of successful businesses
                already using VendorHub.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={onGetStarted}
                size="lg"
                className="h-14 px-8 rounded-2xl bg-primary hover:bg-primary/90 text-lg"
              >
                Start Free Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={onLearnMore}
                className="h-14 px-8 rounded-2xl text-lg"
              >
                Learn More
              </Button>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-primary">
                    {stat.number}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="relative">
            <div className="aspect-square rounded-3xl overflow-hidden shadow-2xl">
              <ImageWithFallback
                src="https://images.unsplash.com/photo-1579389248774-07907f421a6b?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxtb2Rlcm4lMjBidXNpbmVzcyUyMHRlYW0lMjBjb2xsYWJvcmF0aW9ufGVufDF8fHx8MTc1Njg4Mjc0OXww&ixlib=rb-4.1.0&q=80&w=1080"
                alt="Modern business collaboration"
                className="w-full h-full object-cover"
              />
            </div>
            <Card className="absolute -top-4 -left-4 p-4 bg-card shadow-lg rounded-2xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">Verified</div>
                  <div className="text-sm text-muted-foreground">
                    100% Trusted
                  </div>
                </div>
              </div>
            </Card>
            <Card className="absolute -bottom-4 -right-4 p-4 bg-card shadow-lg rounded-2xl">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <div className="font-semibold">300% Growth</div>
                  <div className="text-sm text-muted-foreground">
                    Average increase
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
