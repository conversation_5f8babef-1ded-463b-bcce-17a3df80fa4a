"use client";

import React, { useEffect, useState } from "react";
import Footer from "@/components/marketing/Footer";
import Navbar from "@/components/marketing/Navbar";

export default function LayoutFile({ children }) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const navigationItems = isLoggedIn
    ? [
        { label: "Home", href: "/" },
        // { label: "Search", href: "/search" },
        { label: "Categories", href: "/categories" },
        { label: "About Us", href: "/about-us" },
        { label: "Contact", href: "/contact" },
        { label: "My Enquiries", href: "/customer/enquiries" },
        { label: "Profile", href: "/customer/profile" },
      ]
    : [
        { label: "Home", href: "/" },
        // { label: "Search", href: "/search" },
        { label: "Categories", href: "/categories" },
        { label: "About Us", href: "/about-us" },
        { label: "Contact", href: "/contact" },
      ];

  useEffect(() => {
    const isLoggedIn = localStorage?.getItem("isLoggedIn") || false;
    if (isLoggedIn) {
      setIsLoggedIn(true);
    }
  }, []);

  return (
    <>
      <Navbar navigationItems={navigationItems} />
      <div className="mt-18 pb-6">{children}</div>
      <Footer />
    </>
  );
}
