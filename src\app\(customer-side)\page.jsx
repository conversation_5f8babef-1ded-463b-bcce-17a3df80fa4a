"use client";

import LocationSelector from "@/components/blocks/LocationSelector";
import Banners from "./components/Banners";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import CategoriesList from "./components/category-list";
import PopularVendors from "./components/popularVendors";

export default function Home() {
  return (
    <div>
      <section className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white h-[350px] flex justify-center items-center">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Find Everything You Need
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-white/70">
              Discover amazing products at unbeatable prices
            </p>

            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-center">
              <LocationSelector />
              {/* Search Bar */}
              <div className="max-w-2xl w-full relative">
                <div className="flex items-center bg-transparent dark:bg-input/30 dark:hover:bg-input/50 border border-input rounded-md shadow-lg">
                  <Search className="w-5 h-5 text-background dark:text-muted-foreground ml-4" />
                  <Input
                    placeholder="Search for vendors or categories..."
                    className="bg-transparent! dark:bg-input/30 dark:hover:bg-input/50 placeholder:text-white/60! flex-1 border-0! focus:ring-0!"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Banners */}
      <div className="w-full flex items-center justify-center p-6">
        <Banners />
      </div>

      {/* Categories */}
      <CategoriesList />

      {/* Popular Vendors */}
      <PopularVendors />

      {/*
      <div className="w-full flex items-center justify-center p-6">
        <div className="w-full grid md:grid-cols-2 gap-4">
          {categories
            .sort(() => Math.random() - 0.5)
            .slice(0, 4)
            .map((category) => (
              <SubCategoryGroup key={category.id} category={category} />
            ))}
        </div>
      </div>
      */}
    </div>
  );
}
