import React, { useEffect, useState } from "react";
import {
  Building2,
  DollarSign,
  Shield,
  Users,
  WalletCards,
} from "lucide-react";
import { Card } from "@/components/ui/card";

export default function StatsCard({
  vendors,
  categories,
  salesExecs,
  payments,
}) {
  const [stats, setStats] = useState({
    totalVendors: 0,
    totalCategories: 0,
    totalSalesExecs: 0,
    totalCommission: 0,
  });

  useEffect(() => {
    setStats({
      totalVendors: vendors?.length || 0,
      totalCategories: categories?.length || 0,
      totalSalesExecs: salesExecs?.length || 0,
      totalCommission: payments?.reduce((acc, p) => acc + p.commission, 0) || 0,
    });
  }, [vendors, categories, salesExecs, payments]);

  const numberFormat = (number) => {
    return Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(number);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card className="p-4 rounded-2xl">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Building2 className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalVendors?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Vendors</div>
        </div>
      </Card>

      <Card className="p-4 rounded-2xl">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Shield className="w-5 h-5 text-primary" />
            <span className="text-sm text-muted-foreground">Active</span>
          </div>
          <div className="text-2xl font-bold">
            {stats.totalSalesExecs?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Total Sales Executives
          </div>
        </div>
      </Card>

      <Card className="p-4 rounded-2xl">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <WalletCards className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalCategories.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Categories</div>
        </div>
      </Card>

      <Card className="p-4 rounded-2xl">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <DollarSign className="w-5 h-5 text-red-500" />
          </div>
          <div className="text-2xl font-bold">
            {numberFormat(stats.totalCommission)}
          </div>
          <div className="text-sm text-muted-foreground">Total Commission</div>
        </div>
      </Card>
    </div>
  );
}
