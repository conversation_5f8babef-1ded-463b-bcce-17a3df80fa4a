"use client";

import { useMemo, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { PLANS as plansConst } from "@/constants/plans";

export default function PlansManagementPage() {
  const [items, setItems] = useState(plansConst);
  const [query, setQuery] = useState("");
  const [billing, setBilling] = useState("all");
  const [open, setOpen] = useState(false);
  const [editing, setEditing] = useState(null);
  const [form, setForm] = useState({
    name: "",
    price: 0,
    billing: "monthly",
    features: "",
  });

  const filtered = useMemo(() => {
    let data = [...items];
    if (query)
      data = data.filter((p) =>
        p.name.toLowerCase().includes(query.toLowerCase())
      );
    if (billing !== "all") data = data.filter((p) => p.billing === billing);
    return data;
  }, [items, query, billing]);

  const openCreate = () => {
    setEditing(null);
    setForm({ name: "", price: 0, billing: "monthly", features: "" });
    setOpen(true);
  };
  const openEdit = (p) => {
    setEditing(p);
    setForm({
      name: p.name,
      price: p.price,
      billing: p.billing,
      features: p.features.join("\n"),
    });
    setOpen(true);
  };
  const save = () => {
    if (!form.name.trim()) return setOpen(false);
    if (editing) {
      setItems((prev) =>
        prev.map((it) =>
          it.id === editing.id
            ? {
                ...it,
                name: form.name,
                price: Number(form.price),
                billing: form.billing,
                features: form.features.split(/\n+/).filter(Boolean),
              }
            : it
        )
      );
    } else {
      const nextId = Math.max(...items.map((i) => i.id)) + 1;
      setItems((prev) => [
        ...prev,
        {
          id: nextId,
          name: form.name,
          price: Number(form.price),
          billing: form.billing,
          features: form.features.split(/\n+/).filter(Boolean),
        },
      ]);
    }
    setOpen(false);
  };

  const toggleDisable = (id) => {
    // purely UI: append "(Disabled)" to name to indicate state
    setItems((prev) =>
      prev.map((p) =>
        p.id === id
          ? {
              ...p,
              name: p.name.endsWith(" (Disabled)")
                ? p.name.replace(" (Disabled)", "")
                : `${p.name} (Disabled)`,
            }
          : p
      )
    );
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between gap-3">
        <h1 className="text-2xl font-bold">Plans</h1>
        <div className="flex items-center gap-3 w-full sm:w-auto">
          <Input
            placeholder="Search plans..."
            className="rounded-xl sm:w-64"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <Select value={billing} onValueChange={setBilling}>
            <SelectTrigger className="w-36 rounded-xl">
              <SelectValue placeholder="Billing" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Billing</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button className="rounded-xl" onClick={openCreate}>
                Create Plan
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editing ? "Edit Plan" : "Create Plan"}
                </DialogTitle>
              </DialogHeader>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <label className="text-sm">Name</label>
                  <Input
                    className="mt-1"
                    value={form.name}
                    onChange={(e) =>
                      setForm((f) => ({ ...f, name: e.target.value }))
                    }
                  />
                </div>
                <div>
                  <label className="text-sm">Price (USD)</label>
                  <Input
                    type="number"
                    className="mt-1"
                    value={form.price}
                    onChange={(e) =>
                      setForm((f) => ({ ...f, price: e.target.value }))
                    }
                  />
                </div>
                <div>
                  <label className="text-sm">Billing</label>
                  <Select
                    value={form.billing}
                    onValueChange={(v) =>
                      setForm((f) => ({ ...f, billing: v }))
                    }
                  >
                    <SelectTrigger className="mt-1 rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="sm:col-span-2">
                  <label className="text-sm">Features (one per line)</label>
                  <Textarea
                    className="mt-1"
                    rows={4}
                    value={form.features}
                    onChange={(e) =>
                      setForm((f) => ({ ...f, features: e.target.value }))
                    }
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={save}>{editing ? "Save" : "Create"}</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {filtered.map((p) => (
          <Card key={p.id} className="p-6 rounded-2xl">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold">{p.name}</h3>
              <Badge variant="outline" className="capitalize">
                {p.billing}
              </Badge>
            </div>
            <div className="mt-2 text-3xl font-bold">
              ${p.price}
              <span className="text-base font-medium text-muted-foreground">
                /mo
              </span>
            </div>
            <ul className="mt-4 space-y-2 text-sm list-disc list-inside">
              {p.features.map((f) => (
                <li key={f}>{f}</li>
              ))}
            </ul>
            <div className="mt-6 flex gap-2">
              <Button size="sm" onClick={() => openEdit(p)}>
                Edit
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => toggleDisable(p.id)}
              >
                Disable
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
