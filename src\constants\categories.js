// Categories for Vendors
export const categories = [
  {
    id: 1,
    icon: "https://cdn-icons-png.flaticon.com/512/3659/3659899.png",
    name: "Electronics",
    count: 1240,
    isActive: true,
    slug: "electronics",
    description:
      "Latest gadgets, smartphones, laptops, and electronic accessories",
  },
  {
    id: 2,
    icon: "https://cdn-icons-png.flaticon.com/512/7417/7417708.png",
    name: "Fashion",
    count: 2180,
    isActive: true,
    slug: "fashion",
    description: "Trendy clothing, shoes, accessories for men, women, and kids",
  },
  {
    id: 3,
    icon: "https://cdn-icons-png.flaticon.com/128/3081/3081559.png",
    name: "Home & Garden",
    count: 890,
    isActive: true,
    slug: "home-garden",
    description: "Furniture, decor, kitchen appliances, and garden supplies",
  },
  {
    id: 4,
    icon: "https://cdn-icons-png.flaticon.com/512/1807/1807383.png",
    name: "Health & Beauty",
    count: 650,
    isActive: true,
    slug: "health-beauty",
    description: "Skincare, makeup, wellness products, and health supplements",
  },
  {
    id: 5,
    icon: "https://cdn-icons-png.flaticon.com/512/3311/3311579.png",
    name: "Sports",
    count: 420,
    isActive: true,
    slug: "sports",
    description: "Fitness equipment, outdoor gear, and sports accessories",
  },
  {
    id: 6,
    icon: "https://cdn-icons-png.flaticon.com/512/3195/3195534.png",
    name: "Books & Media",
    count: 350,
    isActive: true,
    slug: "books-media",
    description: "Books, movies, music, and educational materials",
  },
];

export const subCategories = [
  // Electronics
  {
    id: 1,
    name: "Laptops & Desktops",
    count: 120,
    categories: {
      id: 1,
      icon: "https://cdn-icons-png.flaticon.com/512/3659/3659899.png",
      name: "Electronics",
      count: 1240,
      isActive: true,
      slug: "electronics",
      description:
        "Latest gadgets, smartphones, laptops, and electronic accessories",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/2460/2460229.png",
    coverImage:
      "https://img.freepik.com/free-photo/computer-laptop-desk_23-2147785084.jpg",
    isActive: true,
  },
  {
    id: 2,
    name: "Smartphones",
    count: 60,
    categories: {
      id: 1,
      icon: "https://cdn-icons-png.flaticon.com/512/3659/3659899.png",
      name: "Electronics",
      count: 1240,
      isActive: true,
      slug: "electronics",
      description:
        "Latest gadgets, smartphones, laptops, and electronic accessories",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/186/186239.png",
    coverImage:
      "https://img.freepik.com/free-vector/panoramic-view-smartphones_52683-28258.jpg",
    isActive: true,
  },
  {
    id: 3,
    name: "Accessories",
    count: 45,
    categories: {
      id: 1,
      icon: "https://cdn-icons-png.flaticon.com/512/3659/3659899.png=st=1757314861~exp=1757318461~hmac=0a9fc1d0e6877e6ac49ba469f675ea1ba6c88f61ecd85f2a835720f62c7de20d&w=1060",
      name: "Electronics",
      count: 1240,
      isActive: true,
      slug: "electronics",
      description:
        "Latest gadgets, smartphones, laptops, and electronic accessories",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/1606/1606173.png",
    coverImage:
      "https://img.freepik.com/free-photo/modern-stationary-collection-arrangement_23-2149309652.jpg?t=st=1757314861~exp=1757318461~hmac=0a9fc1d0e6877e6ac49ba469f675ea1ba6c88f61ecd85f2a835720f62c7de20d&w=1060",
    isActive: true,
  },

  // Fashion
  {
    id: 4,
    name: "Men's Clothing",
    count: 100,
    categories: {
      id: 2,
      icon: "https://cdn-icons-png.flaticon.com/512/7417/7417708.png",
      name: "Fashion",
      count: 2180,
      isActive: true,
      slug: "fashion",
      description:
        "Trendy clothing, shoes, accessories for men, women, and kids",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/13540/13540227.png",
    coverImage:
      "https://img.freepik.com/free-photo/young-handsome-man-choosing-hat-shop_1303-19839.jpg",
    isActive: true,
  },
  {
    id: 5,
    name: "Women's Fashion",
    count: 120,
    categories: {
      id: 2,
      icon: "https://cdn-icons-png.flaticon.com/512/7417/7417708.png",
      name: "Fashion",
      count: 2180,
      isActive: true,
      slug: "fashion",
      description:
        "Trendy clothing, shoes, accessories for men, women, and kids",
    },
    icon: "https://cdn-icons-png.flaticon.com/128/3541/3541569.png",
    coverImage:
      "https://img.freepik.com/free-photo/two-young-beautiful-women-friends-together-isolated-yellow-black-yellow-dress-hat-stylish-boho-having-fun_285396-10294.jpg",
    isActive: true,
  },
  {
    id: 6,
    name: "Kid's Wear",
    count: 80,
    categories: {
      id: 2,
      icon: "https://cdn-icons-png.flaticon.com/512/7417/7417708.png",
      name: "Fashion",
      count: 2180,
      isActive: true,
      slug: "fashion",
      description:
        "Trendy clothing, shoes, accessories for men, women, and kids",
    },
    icon: "https://cdn-icons-png.flaticon.com/128/9752/9752768.png",
    coverImage:
      "https://img.freepik.com/free-photo/dancing-team-studio_1303-10936.jpg",
    isActive: true,
  },

  // Home & Garden
  {
    id: 7,
    name: "Furniture",
    count: 90,
    categories: {
      id: 3,
      icon: "https://cdn-icons-png.flaticon.com/128/3081/3081559.png",
      name: "Home & Garden",
      count: 890,
      isActive: true,
      slug: "home-garden",
      description: "Furniture, decor, kitchen appliances, and garden supplies",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/5540/5540319.png",
    coverImage:
      "https://img.freepik.com/free-photo/minimalist-black-interior-with-black-sofa_1268-31803.jpg",
    isActive: true,
  },
  {
    id: 8,
    name: "Kitchen Tools",
    count: 70,
    categories: {
      id: 3,
      icon: "https://cdn-icons-png.flaticon.com/128/3081/3081559.png",
      name: "Home & Garden",
      count: 890,
      isActive: true,
      slug: "home-garden",
      description: "Furniture, decor, kitchen appliances, and garden supplies",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/11209/11209344.png",
    coverImage:
      "https://img.freepik.com/free-photo/flat-lay-kitchen-utensils-arrangement_23-2149491471.jpg",
    isActive: true,
  },
  {
    id: 9,
    name: "Gardening Tools",
    count: 60,
    categories: {
      id: 3,
      icon: "https://cdn-icons-png.flaticon.com/128/3081/3081559.png",
      name: "Home & Garden",
      count: 890,
      isActive: true,
      slug: "home-garden",
      description: "Furniture, decor, kitchen appliances, and garden supplies",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/11166/11166891.png",
    coverImage:
      "https://img.freepik.com/free-photo/tidy-gardening-elements_23-**********.jpg",
    isActive: true,
  },

  // Healthcare
  {
    id: 10,
    name: "Skincare",
    count: 75,
    categories: {
      id: 4,
      icon: "https://cdn-icons-png.flaticon.com/512/1807/1807383.png",
      name: "Health & Beauty",
      count: 650,
      isActive: true,
      slug: "health-beauty",
      description:
        "Skincare, makeup, wellness products, and health supplements",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/12584/12584994.png",
    coverImage:
      "https://img.freepik.com/free-photo/still-life-care-products_23-**********.jpg",
    isActive: true,
  },
  {
    id: 11,
    name: "Makeup",
    count: 65,
    categories: {
      id: 4,
      icon: "https://cdn-icons-png.flaticon.com/512/1807/1807383.png",
      name: "Health & Beauty",
      count: 650,
      isActive: true,
      slug: "health-beauty",
      description:
        "Skincare, makeup, wellness products, and health supplements",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/3501/3501241.png",
    coverImage:
      "https://img.freepik.com/free-photo/professional-makeup-tools-colored-background_23-**********.jpg",
    isActive: true,
  },
  {
    id: 12,
    name: "Supplements",
    count: 55,
    categories: {
      id: 4,
      icon: "https://cdn-icons-png.flaticon.com/512/1807/1807383.png",
      name: "Health & Beauty",
      count: 650,
      isActive: true,
      slug: "health-beauty",
      description:
        "Skincare, makeup, wellness products, and health supplements",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/6750/6750843.png",
    coverImage:
      "https://img.freepik.com/free-photo/protein-gym_23-**********.jpg",
    isActive: true,
  },

  // Sports & Fitness
  {
    id: 13,
    name: "Fitness Gear",
    count: 85,
    categories: {
      id: 5,
      icon: "https://cdn-icons-png.flaticon.com/512/3311/3311579.png",
      name: "Sports & Fitness",
      count: 420,
      isActive: true,
      slug: "sports",
      description: "Fitness equipment, outdoor gear, and sports accessories",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/18302/18302673.png",
    coverImage:
      "https://img.freepik.com/free-photo/top-view-perfectly-ordered-fitness-items_23-**********.jpg",
    isActive: true,
  },
  {
    id: 14,
    name: "Outdoor Equipment",
    count: 90,
    categories: {
      id: 5,
      icon: "https://cdn-icons-png.flaticon.com/512/3311/3311579.png",
      name: "Sports & Fitness",
      count: 420,
      isActive: true,
      slug: "sports",
      description: "Fitness equipment, outdoor gear, and sports accessories",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/8964/8964683.png",
    coverImage:
      "https://img.freepik.com/free-photo/close-up-athlete-playing-soccer_23-2150845600.jpg",
    isActive: true,
  },
  {
    id: 15,
    name: "Sportwear",
    count: 70,
    categories: {
      id: 5,
      icon: "https://cdn-icons-png.flaticon.com/512/3311/3311579.png",
      name: "Sports & Fitness",
      count: 420,
      isActive: true,
      slug: "sports",
      description: "Fitness equipment, outdoor gear, and sports accessories",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/6343/6343392.png",
    coverImage:
      "https://img.freepik.com/free-photo/portrait-professional-soccer-player_93675-134403.jpg",
    isActive: true,
  },

  // Books & Media
  {
    id: 16,
    name: "Fiction",
    count: 60,
    categories: {
      id: 6,
      icon: "https://cdn-icons-png.flaticon.com/512/3195/3195534.png",
      name: "Books & Media",
      count: 350,
      isActive: true,
      slug: "books-media",
      description: "Books, movies, music, and educational materials",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/2570/2570219.png",
    coverImage:
      "https://img.freepik.com/free-photo/open-book-with-fairytale-scene_52683-107846.jpg",
    isActive: true,
  },
  {
    id: 17,
    name: "Educational",
    count: 50,
    categories: {
      id: 6,
      icon: "https://cdn-icons-png.flaticon.com/512/3195/3195534.png",
      name: "Books & Media",
      count: 350,
      isActive: true,
      slug: "books-media",
      description: "Books, movies, music, and educational materials",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/3976/3976625.png",
    coverImage:
      "https://img.freepik.com/free-photo/various-books-with-spectacles-table_1252-713.jpg",
    isActive: true,
  },
  {
    id: 18,
    name: "Audio Books",
    count: 40,
    categories: {
      id: 6,
      icon: "https://cdn-icons-png.flaticon.com/512/3195/3195534.png",
      name: "Books & Media",
      count: 350,
      isActive: true,
      slug: "books-media",
      description: "Books, movies, music, and educational materials",
    },
    icon: "https://cdn-icons-png.flaticon.com/512/3213/3213211.png",
    coverImage:
      "https://img.freepik.com/free-photo/concept-audiobook-books-table-with-headphones-put-them_1423-146.jpg",
    isActive: true,
  },
];
