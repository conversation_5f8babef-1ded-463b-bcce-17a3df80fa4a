"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { apiKeys } from "@/constants";

import { useState } from "react";
import { Button } from "../ui/button";

export default function LocationDropdown() {
  const recentLocations = ["Antarli, Thane"];
  const trendingAreas = [
    "Mira Road East, Thane",
    "Kandivali, Mumbai",
    "Malad West, Mumbai",
    "Kandivali West, Mumbai",
    "Thane West, Thane",
    "Nalasopara East, Palghar",
  ];

  const [selectedLocation, setSelectedLocation] = useState(recentLocations[0]);
  const [isDetecting, setIsDetecting] = useState(false);

  const detectLocation = async () => {
    setIsDetecting(true);

    if (!navigator.geolocation) {
      alert("Geolocation is not supported by your browser.");
      setIsDetecting(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        const apiKey = apiKeys.find((key) => key.provider === "Radar")?.key;

        try {
          // -- Open Cage API --
          // `https://api.opencagedata.com/geocode/v1/json?q=${latitude}+${longitude}&key=${apiKey}`
          // -- Geoapify API --
          // `https://api.geoapify.com/v1/geocode/reverse?lat=${latitude}&lon=${longitude}&type=city&lang=en&format=json&apiKey=${apiKey}`
          // -- Location IQ API --
          // `https://us1.locationiq.com/v1/reverse.php?key=${apiKey}&lat=${latitude}&lon=${longitude}&format=json`
          const res = await fetch(
            `https://api.radar.io/v1/geocode/reverse?coordinates=${latitude},${longitude}`,
            {
              headers: {
                Authorization: apiKey,
              },
            }
          );
          const data = await res.json();
          console.log(data);

          const address = data?.addresses?.[0]?.formattedAddress;
          if (address) {
            setSelectedLocation(address);
          } else {
            alert("Unable to detect a readable location.");
          }
        } catch (error) {
          console.error("Geocoding error:", error);
          alert("Failed to detect location.");
        } finally {
          setIsDetecting(false);
        }
      },
      (error) => {
        console.error("Geolocation error:", error);
        alert("Failed to get your location.");
        setIsDetecting(false);
      }
    );
  };

  const handleSelect = async (value) => {
    if (value === "detect") {
      await detectLocation();
    } else {
      setSelectedLocation(value);
    }
  };

  return (
    <DropdownMenu className="min-w-[350px] w-[350px] space-y-2">
      <DropdownMenuTrigger asChild>
        <Button variant={"transparent"} className={"text-sm"}>
          {isDetecting ? "Detecting..." : selectedLocation || "Select Location"}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-full">
        <DropdownMenuItem onClick={() => handleSelect("detect")}>
          📡 {isDetecting ? "Detecting..." : "Detect My Location"}
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Recent Locations
        </DropdownMenuLabel>
        {recentLocations.map((loc, i) => (
          <DropdownMenuItem key={i} onClick={() => handleSelect(loc)}>
            {loc}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Trending Areas
        </DropdownMenuLabel>
        {trendingAreas.map((area, i) => (
          <DropdownMenuItem key={i} onClick={() => handleSelect(area)}>
            {area}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
