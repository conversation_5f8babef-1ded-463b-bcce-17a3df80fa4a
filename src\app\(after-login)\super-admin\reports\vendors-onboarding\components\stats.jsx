import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { CircleX, Store, CircleCheckBig } from "lucide-react";

export default function StatsCard({ vendors, setFilter }) {
  const [stats, setStats] = useState({
    totalVendors: 0,
    totalVerifiedVendors: 0,
    totalNonVerifiedVendors: 0,
  });

  useEffect(() => {
    const verifiedVendors = vendors.filter((v) => v.verified);
    const nonVerifiedVendors = vendors.filter((v) => !v.verified);
    setStats({
      totalVendors: vendors?.length || 0,
      totalVerifiedVendors: verifiedVendors?.length || 0,
      totalNonVerifiedVendors: nonVerifiedVendors?.length || 0,
    });
  }, [vendors]);

  const numberFormat = (number) => {
    return Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(number);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Store className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalVendors?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Vendors</div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("verified")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CircleCheckBig className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalVerifiedVendors.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Total Verified Vendors
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors shimmer duration-300"
        onClick={() => setFilter("non-verified")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CircleX className="w-5 h-5 text-destructive" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalNonVerifiedVendors?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Total Non Verified Vendors
          </div>
        </div>
      </Card>
    </div>
  );
}
