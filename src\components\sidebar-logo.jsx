"use client";

import * as React from "react";
import { Building2 } from "lucide-react";
import { SidebarMenu, SidebarMenuItem } from "@/components/ui/sidebar";
import { CompanyName } from "@/constants/companyName";

export function SidebarLogo({ description = "Super Admin Portal", user }) {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <div
          size="lg"
          className="flex h-10 w-full items-center rounded-lg gap-3 data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <div className="bg-primary text-white flex aspect-square size-8 items-center justify-center rounded-lg">
            <Building2 className="size-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-medium">{CompanyName}</span>
            <span className="truncate text-xs text-muted-foreground">
              {user?.role} Portal
            </span>
          </div>
        </div>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
