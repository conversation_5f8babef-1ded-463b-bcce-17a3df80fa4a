"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { useCart } from "@/contexts/CartContext";
import { ShoppingCart, Check, Plus } from "lucide-react";
import { toast } from "sonner";

export function AddToCartButton({ 
  product, 
  quantity = 1, 
  selectedSize = null, 
  selectedColor = null,
  variant = "default",
  size = "default",
  className = "",
  children,
  showIcon = true,
  disabled = false
}) {
  const { addToCart, isInCart } = useCart();
  const [isAdding, setIsAdding] = useState(false);
  const [justAdded, setJustAdded] = useState(false);

  const inCart = isInCart(product.id, selectedSize, selectedColor);

  const handleAddToCart = async () => {
    if (disabled || !product.inStock) return;

    setIsAdding(true);
    
    try {
      addToCart(product, quantity, selectedSize, selectedColor);
      
      setJustAdded(true);
      
      // Show success toast
      toast.success(`${product.name} added to cart!`, {
        description: `Quantity: ${quantity}${selectedSize ? `, Size: ${selectedSize}` : ''}${selectedColor ? `, Color: ${selectedColor}` : ''}`,
        action: {
          label: "View Cart",
          onClick: () => {
            // This would open the cart sidebar or navigate to cart page
            console.log("View cart clicked");
          },
        },
      });

      // Reset the "just added" state after 2 seconds
      setTimeout(() => {
        setJustAdded(false);
      }, 2000);
      
    } catch (error) {
      console.error("Error adding to cart:", error);
      toast.error("Failed to add item to cart");
    } finally {
      setIsAdding(false);
    }
  };

  // If product is out of stock
  if (!product.inStock) {
    return (
      <Button 
        variant="outline" 
        size={size} 
        className={className}
        disabled
      >
        Out of Stock
      </Button>
    );
  }

  // If item is already in cart and we just added it
  if (justAdded) {
    return (
      <Button 
        variant="outline" 
        size={size} 
        className={`${className} border-green-500 text-green-600`}
        disabled
      >
        <Check className="w-4 h-4 mr-2" />
        Added to Cart
      </Button>
    );
  }

  // If item is already in cart
  if (inCart) {
    return (
      <Button 
        variant="outline" 
        size={size} 
        className={className}
        onClick={handleAddToCart}
        disabled={isAdding}
      >
        {showIcon && <Plus className="w-4 h-4 mr-2" />}
        {children || "Add More"}
      </Button>
    );
  }

  // Default add to cart button
  return (
    <Button 
      variant={variant} 
      size={size} 
      className={className}
      onClick={handleAddToCart}
      disabled={isAdding || disabled}
    >
      {isAdding ? (
        <>
          <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
          Adding...
        </>
      ) : (
        <>
          {showIcon && <ShoppingCart className="w-4 h-4 mr-2" />}
          {children || "Add to Cart"}
        </>
      )}
    </Button>
  );
}

export default AddToCartButton;
