// Admin reports sample data

export const userActivityReport = {
  totals: { logins: 1240, actions: 5320, failures: 24 },
  recent: [
    { id: 1, user: "<PERSON>", action: "Approved vendor", time: "2h" },
    { id: 2, user: "<PERSON>", action: "Created enquiry", time: "4h" },
    { id: 3, user: "<PERSON>", action: "Exported report", time: "1d" },
  ],
};

export const revenueReport = {
  summary: { today: 297, thisWeek: 1386, thisMonth: 4125 },
  byPlan: [
    { plan: "Starter", count: 38, revenue: 3762 },
    { plan: "Pro", count: 8, revenue: 1592 },
    { plan: "Enterprise", count: 2, revenue: 998 },
  ],
};

// Enhanced user activity data for detailed reporting
export const displayUserActivityData = [
  {
    id: 1,
    userName: "<PERSON>",
    userEmail: "<EMAIL>",
    action: "Approved vendor",
    actionType: "approval",
    targetEntity: "<PERSON>'s Italian Kitchen",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    sessionId: "sess_abc123",
    success: true,
    users: {
      id: 1,
      username: "sarah_w",
      firstName: "<PERSON>",
      last<PERSON>ame: "<PERSON>",
    },
    created: "2025-01-15T10:30:00Z",
  },
  {
    id: 2,
    userName: "Mike Johnson",
    userEmail: "<EMAIL>",
    action: "Created enquiry",
    actionType: "create",
    targetEntity: "TechFix Solutions",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
    sessionId: "sess_def456",
    success: true,
    users: {
      id: 2,
      username: "mike_j",
      firstName: "Mike",
      lastName: "Johnson",
    },
    created: "2025-01-14T14:20:00Z",
  },
  {
    id: 3,
    userName: "John Smith",
    userEmail: "<EMAIL>",
    action: "Exported report",
    actionType: "export",
    targetEntity: "Customer Registrations Report",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (X11; Linux x86_64)",
    sessionId: "sess_ghi789",
    success: true,
    users: {
      id: 3,
      username: "john_s",
      firstName: "John",
      lastName: "Smith",
    },
    created: "2025-01-13T09:15:00Z",
  },
  {
    id: 4,
    userName: "Emily Davis",
    userEmail: "<EMAIL>",
    action: "Updated vendor profile",
    actionType: "update",
    targetEntity: "Green Garden Landscaping",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    sessionId: "sess_jkl012",
    success: true,
    users: {
      id: 4,
      username: "emily_d",
      firstName: "Emily",
      lastName: "Davis",
    },
    created: "2025-01-12T16:45:00Z",
  },
  {
    id: 5,
    userName: "Robert Brown",
    userEmail: "<EMAIL>",
    action: "Failed login attempt",
    actionType: "login",
    targetEntity: "Admin Dashboard",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
    sessionId: "sess_mno345",
    success: false,
    users: {
      id: 5,
      username: "robert_b",
      firstName: "Robert",
      lastName: "Brown",
    },
    created: "2025-01-11T11:30:00Z",
  },
  {
    id: 6,
    userName: "Lisa Anderson",
    userEmail: "<EMAIL>",
    action: "Deleted enquiry",
    actionType: "delete",
    targetEntity: "Enquiry #1234",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    sessionId: "sess_pqr678",
    success: true,
    users: {
      id: 6,
      username: "lisa_a",
      firstName: "Lisa",
      lastName: "Anderson",
    },
    created: "2025-01-10T13:20:00Z",
  },
  {
    id: 7,
    userName: "David Wilson",
    userEmail: "<EMAIL>",
    action: "Generated analytics report",
    actionType: "export",
    targetEntity: "Revenue Analytics",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
    sessionId: "sess_stu901",
    success: true,
    users: {
      id: 7,
      username: "david_w",
      firstName: "David",
      lastName: "Wilson",
    },
    created: "2025-01-09T08:45:00Z",
  },
  {
    id: 8,
    userName: "Jennifer Taylor",
    userEmail: "<EMAIL>",
    action: "Rejected vendor application",
    actionType: "rejection",
    targetEntity: "ABC Services",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (X11; Linux x86_64)",
    sessionId: "sess_vwx234",
    success: true,
    users: {
      id: 8,
      username: "jennifer_t",
      firstName: "Jennifer",
      lastName: "Taylor",
    },
    created: "2025-01-08T15:10:00Z",
  },
  {
    id: 9,
    userName: "Michael Garcia",
    userEmail: "<EMAIL>",
    action: "Updated system settings",
    actionType: "update",
    targetEntity: "Email Configuration",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    sessionId: "sess_yza567",
    success: true,
    users: {
      id: 9,
      username: "michael_g",
      firstName: "Michael",
      lastName: "Garcia",
    },
    created: "2025-01-07T12:25:00Z",
  },
  {
    id: 10,
    userName: "Amanda Martinez",
    userEmail: "<EMAIL>",
    action: "Logged in successfully",
    actionType: "login",
    targetEntity: "Admin Dashboard",
    ipAddress: "*************",
    userAgent: "Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X)",
    sessionId: "sess_bcd890",
    success: true,
    users: {
      id: 10,
      username: "amanda_m",
      firstName: "Amanda",
      lastName: "Martinez",
    },
    created: "2025-01-06T17:30:00Z",
  },
];

// Enhanced revenue data for detailed reporting
export const displayRevenueData = [
  {
    id: 1,
    vendorName: "Giuseppe's Italian Kitchen",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    paymentDate: "2025-01-15",
    paymentMethod: "Credit Card",
    transactionId: "TXN001ABC",
    commission: 9.9,
    netRevenue: 89.1,
    users: {
      id: 1,
      username: "mario_g",
      firstName: "Mario",
      lastName: "Giuseppe",
    },
    created: "2025-01-15T10:30:00Z",
  },
  {
    id: 2,
    vendorName: "TechFix Solutions",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "monthly",
    amount: 199,
    currency: "USD",
    paymentDate: "2025-01-14",
    paymentMethod: "Debit Card",
    transactionId: "TXN002DEF",
    commission: 19.9,
    netRevenue: 179.1,
    users: {
      id: 2,
      username: "john_s",
      firstName: "John",
      lastName: "Smith",
    },
    created: "2025-01-14T14:20:00Z",
  },
  {
    id: 3,
    vendorName: "Green Garden Landscaping",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    paymentDate: "2025-01-13",
    paymentMethod: "Credit Card",
    transactionId: "TXN003GHI",
    commission: 9.9,
    netRevenue: 89.1,
    users: {
      id: 3,
      username: "lisa_s",
      firstName: "Lisa",
      lastName: "Smith",
    },
    created: "2025-01-13T09:15:00Z",
  },
  {
    id: 4,
    vendorName: "City Plumbing Services",
    vendorEmail: "<EMAIL>",
    planName: "Enterprise",
    planType: "yearly",
    amount: 4990,
    currency: "USD",
    paymentDate: "2025-01-12",
    paymentMethod: "Bank Transfer",
    transactionId: "TXN004JKL",
    commission: 499,
    netRevenue: 4491,
    users: {
      id: 4,
      username: "mike_j",
      firstName: "Mike",
      lastName: "Johnson",
    },
    created: "2025-01-12T16:45:00Z",
  },
  {
    id: 5,
    vendorName: "Metro Electronics",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "monthly",
    amount: 199,
    currency: "USD",
    paymentDate: "2025-01-11",
    paymentMethod: "Credit Card",
    transactionId: "TXN005MNO",
    commission: 19.9,
    netRevenue: 179.1,
    users: {
      id: 5,
      username: "sarah_w",
      firstName: "Sarah",
      lastName: "Wilson",
    },
    created: "2025-01-11T11:30:00Z",
  },
  {
    id: 6,
    vendorName: "Sunrise Bakery",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    paymentDate: "2025-01-10",
    paymentMethod: "PayPal",
    transactionId: "TXN006PQR",
    commission: 9.9,
    netRevenue: 89.1,
    users: {
      id: 6,
      username: "anna_b",
      firstName: "Anna",
      lastName: "Brown",
    },
    created: "2025-01-10T13:20:00Z",
  },
  {
    id: 7,
    vendorName: "AutoFix Garage",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "monthly",
    amount: 199,
    currency: "USD",
    paymentDate: "2025-01-09",
    paymentMethod: "Credit Card",
    transactionId: "TXN007STU",
    commission: 19.9,
    netRevenue: 179.1,
    users: {
      id: 7,
      username: "tom_d",
      firstName: "Tom",
      lastName: "Davis",
    },
    created: "2025-01-09T08:45:00Z",
  },
  {
    id: 8,
    vendorName: "Fashion Forward Boutique",
    vendorEmail: "<EMAIL>",
    planName: "Enterprise",
    planType: "monthly",
    amount: 499,
    currency: "USD",
    paymentDate: "2025-01-08",
    paymentMethod: "Credit Card",
    transactionId: "TXN008VWX",
    commission: 49.9,
    netRevenue: 449.1,
    users: {
      id: 8,
      username: "emma_t",
      firstName: "Emma",
      lastName: "Taylor",
    },
    created: "2025-01-08T15:10:00Z",
  },
  {
    id: 9,
    vendorName: "Home Improvement Pro",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    paymentDate: "2025-01-07",
    paymentMethod: "Debit Card",
    transactionId: "TXN009YZA",
    commission: 9.9,
    netRevenue: 89.1,
    users: {
      id: 9,
      username: "robert_a",
      firstName: "Robert",
      lastName: "Anderson",
    },
    created: "2025-01-07T12:25:00Z",
  },
  {
    id: 10,
    vendorName: "Digital Marketing Hub",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "yearly",
    amount: 1990,
    currency: "USD",
    paymentDate: "2025-01-06",
    paymentMethod: "Bank Transfer",
    transactionId: "TXN010BCD",
    commission: 199,
    netRevenue: 1791,
    users: {
      id: 10,
      username: "jennifer_m",
      firstName: "Jennifer",
      lastName: "Miller",
    },
    created: "2025-01-06T17:30:00Z",
  },
];

export const customerRegistrationsReport = {
  weekly: [
    { day: "Mon", customers: 24 },
    { day: "Tue", customers: 31 },
    { day: "Wed", customers: 18 },
    { day: "Thu", customers: 36 },
    { day: "Fri", customers: 29 },
    { day: "Sat", customers: 14 },
    { day: "Sun", customers: 12 },
  ],
  total: 164,
};

// Enhanced customer registrations data for detailed reporting
export const displayCustomerRegistrations = [
  {
    id: 1,
    name: "Alice Johnson",
    email: "<EMAIL>",
    phone: "****** 0101",
    city: "New York",
    status: "active",
    verified: true,
    registrationSource: "Website",
    users: {
      id: 1,
      username: "alice_j",
      firstName: "Alice",
      lastName: "Johnson",
    },
    created: "2025-02-15T10:30:00Z",
  },
  {
    id: 2,
    name: "Bob Smith",
    email: "<EMAIL>",
    phone: "****** 0102",
    city: "Los Angeles",
    status: "active",
    verified: true,
    registrationSource: "Mobile App",
    users: {
      id: 2,
      username: "bob_smith",
      firstName: "Bob",
      lastName: "Smith",
    },
    created: "2025-08-14T14:20:00Z",
  },
  {
    id: 3,
    name: "Carol Davis",
    email: "<EMAIL>",
    phone: "****** 0103",
    city: "Chicago",
    status: "pending",
    verified: false,
    registrationSource: "Website",
    users: {
      id: 3,
      username: "carol_d",
      firstName: "Carol",
      lastName: "Davis",
    },
    created: "2025-05-13T09:15:00Z",
  },
  {
    id: 4,
    name: "David Wilson",
    email: "<EMAIL>",
    phone: "****** 0104",
    city: "Houston",
    status: "active",
    verified: true,
    registrationSource: "Social Media",
    users: {
      id: 4,
      username: "david_w",
      firstName: "David",
      lastName: "Wilson",
    },
    created: "2024-11-12T16:45:00Z",
  },
  {
    id: 5,
    name: "Emma Brown",
    email: "<EMAIL>",
    phone: "****** 0105",
    city: "Phoenix",
    status: "active",
    verified: true,
    registrationSource: "Website",
    users: {
      id: 5,
      username: "emma_b",
      firstName: "Emma",
      lastName: "Brown",
    },
    created: "2025-07-11T11:30:00Z",
  },
  {
    id: 6,
    name: "Frank Miller",
    email: "<EMAIL>",
    phone: "****** 0106",
    city: "Philadelphia",
    status: "inactive",
    verified: false,
    registrationSource: "Mobile App",
    users: {
      id: 6,
      username: "frank_m",
      firstName: "Frank",
      lastName: "Miller",
    },
    created: "2025-05-10T13:20:00Z",
  },
  {
    id: 7,
    name: "Grace Taylor",
    email: "<EMAIL>",
    phone: "****** 0107",
    city: "San Antonio",
    status: "active",
    verified: true,
    registrationSource: "Website",
    users: {
      id: 7,
      username: "grace_t",
      firstName: "Grace",
      lastName: "Taylor",
    },
    created: "2025-03-09T08:45:00Z",
  },
  {
    id: 8,
    name: "Henry Anderson",
    email: "<EMAIL>",
    phone: "****** 0108",
    city: "San Diego",
    status: "active",
    verified: true,
    registrationSource: "Referral",
    users: {
      id: 8,
      username: "henry_a",
      firstName: "Henry",
      lastName: "Anderson",
    },
    created: "2025-05-08T15:10:00Z",
  },
  {
    id: 9,
    name: "Ivy Thomas",
    email: "<EMAIL>",
    phone: "****** 0109",
    city: "Dallas",
    status: "pending",
    verified: false,
    registrationSource: "Website",
    users: {
      id: 9,
      username: "ivy_t",
      firstName: "Ivy",
      lastName: "Thomas",
    },
    created: "2025-01-07T12:25:00Z",
  },
  {
    id: 10,
    name: "Jack Jackson",
    email: "<EMAIL>",
    phone: "****** 0110",
    city: "San Jose",
    status: "active",
    verified: true,
    registrationSource: "Mobile App",
    users: {
      id: 10,
      username: "jack_j",
      firstName: "Jack",
      lastName: "Jackson",
    },
    created: "2025-02-06T17:30:00Z",
  },
];
