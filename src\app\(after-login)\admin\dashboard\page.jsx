"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  Users,
  Building2,
  MessageSquare,
  CheckCircle,
  XCircle,
  Settings,
  FileText,
  Star,
  Search,
  Filter,
  Edit,
  Trash2,
  Plus,
} from "lucide-react";
import { adminStats, recentEnquiries } from "@/constants/dashboards";
import { categories } from "@/constants/categories";
import { pendingVendors } from "@/constants/vendors";

export default function AdminDashboard() {
  const [currentView, setCurrentView] = useState("dashboard");

  const renderDashboardView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Manage vendors, customers, and platform operations
          </p>
        </div>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Quick Actions
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Building2 className="w-5 h-5 text-primary" />
              <span className="text-sm text-green-600 font-medium">+5%</span>
            </div>
            <div className="text-2xl font-bold">{adminStats.activeVendors}</div>
            <div className="text-sm text-muted-foreground">Active Vendors</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <MessageSquare className="w-5 h-5 text-accent" />
              <span className="text-sm text-green-600 font-medium">+12%</span>
            </div>
            <div className="text-2xl font-bold">
              {adminStats.enquiriesToday}
            </div>
            <div className="text-sm text-muted-foreground">Enquiries Today</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Users className="w-5 h-5 text-blue-500" />
              <span className="text-sm text-muted-foreground">This month</span>
            </div>
            <div className="text-2xl font-bold">
              {adminStats.vendorsOnboardedBySales}
            </div>
            <div className="text-sm text-muted-foreground">Sales Onboarded</div>
          </div>
        </Card>

        <Card className="p-4 rounded-2xl">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <CheckCircle className="w-5 h-5 text-yellow-500" />
              <Badge
                variant="secondary"
                className="bg-yellow-100 text-yellow-800 text-xs"
              >
                Urgent
              </Badge>
            </div>
            <div className="text-2xl font-bold">
              {adminStats.pendingApprovals}
            </div>
            <div className="text-sm text-muted-foreground">
              Pending Approvals
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                Pending Vendor Approvals
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentView("vendors")}
              >
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {pendingVendors.slice(0, 2).map((vendor) => (
                <div key={vendor.id} className="p-4 bg-muted/30 rounded-xl">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-semibold">{vendor.businessName}</h4>
                        <p className="text-sm text-muted-foreground">
                          {vendor.category} • {vendor.location}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          By {vendor.registeredBy} • {vendor.submittedDate}
                        </p>
                      </div>
                      <Badge className="bg-green-100 text-green-800">
                        Paid
                      </Badge>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        className="h-8 text-xs rounded-lg bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Approve
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs rounded-lg"
                      >
                        <XCircle className="w-3 h-3 mr-1" />
                        Reject
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs rounded-lg"
                      >
                        Review
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>

        <Card className="p-6 rounded-2xl">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Recent Enquiries</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentView("enquiries")}
              >
                View All
              </Button>
            </div>
            <div className="space-y-4">
              {recentEnquiries.map((enquiry) => (
                <div key={enquiry.id} className="p-4 bg-muted/30 rounded-xl">
                  <div className="space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <h4 className="font-semibold text-sm">
                          {enquiry.customer}
                        </h4>
                        <p className="text-xs text-muted-foreground">
                          to {enquiry.vendor}
                        </p>
                      </div>
                      <Badge
                        variant={
                          enquiry.status === "new" ? "default" : "secondary"
                        }
                        className={`text-xs ${
                          enquiry.status === "new"
                            ? "bg-accent text-white"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {enquiry.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-foreground">{enquiry.message}</p>
                    <p className="text-xs text-muted-foreground">
                      {enquiry.date}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderVendorsView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Vendor Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" className="rounded-xl">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button className="rounded-xl bg-primary hover:bg-primary/90">
            Export List
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input placeholder="Search vendors..." className="pl-10 rounded-xl" />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-40 rounded-xl">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Vendors List */}
      <div className="space-y-4">
        {pendingVendors.map((vendor) => (
          <Card key={vendor.id} className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {vendor.businessName}
                    </h3>
                    <p className="text-muted-foreground">
                      Contact: {vendor.contactPerson}
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <span>{vendor.category}</span>
                      <span>•</span>
                      <span>{vendor.location}</span>
                      <span>•</span>
                      <span>Registered by {vendor.registeredBy}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className="bg-green-100 text-green-800">
                    Payment: {vendor.paymentStatus}
                  </Badge>
                  <Badge variant="outline">{vendor.documents} docs</Badge>
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  size="sm"
                  className="rounded-lg bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Approve Vendor
                </Button>
                <Button variant="outline" size="sm" className="rounded-lg">
                  <XCircle className="w-4 h-4 mr-2" />
                  Reject
                </Button>
                <Button variant="ghost" size="sm" className="rounded-lg">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Details
                </Button>
                <Button variant="ghost" size="sm" className="rounded-lg">
                  View Documents
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderCustomersView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Customer Management</h1>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          Export Customers
        </Button>
      </div>

      <div className="text-center py-20 text-muted-foreground">
        <Users className="mx-auto w-12 h-12 mb-4" />
        <h3 className="text-lg font-medium mb-2">Customer Management</h3>
        <p>View and manage customer accounts and activities</p>
      </div>
    </div>
  );

  const renderEnquiriesView = () => (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Enquiries & Reviews</h1>

      <div className="space-y-4">
        {recentEnquiries.map((enquiry) => (
          <Card key={enquiry.id} className="p-6 rounded-2xl">
            <div className="space-y-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Avatar>
                    <AvatarFallback>
                      {enquiry.customer
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{enquiry.customer}</h3>
                    <p className="text-sm text-muted-foreground">
                      to {enquiry.vendor}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {enquiry.date}
                    </p>
                  </div>
                </div>
                <Badge
                  variant={enquiry.status === "new" ? "default" : "secondary"}
                  className={
                    enquiry.status === "new"
                      ? "bg-accent text-white"
                      : "bg-blue-100 text-blue-800"
                  }
                >
                  {enquiry.status}
                </Badge>
              </div>

              <p className="text-foreground">{enquiry.message}</p>

              <div className="flex gap-2">
                <Button size="sm" className="rounded-lg">
                  View Details
                </Button>
                <Button variant="outline" size="sm" className="rounded-lg">
                  Moderate
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderCategoriesView = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Category Management</h1>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Add Category
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {categories.map((category) => (
          <Card key={category.id} className="p-4 rounded-2xl">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">{category.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {category.count} vendors
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={category.isActive ? "default" : "secondary"}
                  className={
                    category.isActive ? "bg-green-100 text-green-800" : ""
                  }
                >
                  {category.isActive ? "Active" : "Inactive"}
                </Badge>
                <Button variant="ghost" size="sm">
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case "dashboard":
        return renderDashboardView();
      case "vendors":
        return renderVendorsView();
      case "customers":
        return renderCustomersView();
      case "enquiries":
        return renderEnquiriesView();
      case "categories":
        return renderCategoriesView();
      default:
        return renderDashboardView();
    }
  };

  const navigationItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3 },
    { id: "vendors", label: "Vendors", icon: Building2 },
    { id: "customers", label: "Customers", icon: Users },
    { id: "enquiries", label: "Enquiries", icon: MessageSquare },
    { id: "categories", label: "Categories", icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Main Content */}
      <main className="container mx-auto p-6">{renderCurrentView()}</main>
    </div>
  );
}
