"use client";

import { useEffect, useState } from "react";
import { DataTable } from "@/components/ui/data-table";
import { Badge } from "@/components/ui/badge";
import ActionComponent from "@/components/ui/action-component";
import DateFilter from "@/components/blocks/DateFilter";
import { displayVendors } from "@/constants";
import { toast } from "sonner";
import { CircleCheckBig, Download } from "lucide-react";
import StatsCard from "./components/stats";
import { GrowthChart } from "./components/chart";
import { Button } from "@/components/ui/button";

export default function VendorsOnboardingReportPage() {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState("all");
  const [chartData, setchartData] = useState([]);

  // Date filter state
  const [dateFilter, setDateFilter] = useState({
    startDate: "",
    endDate: "",
    preset: "week", // Default to last 7 days
  });

  // Function to apply date filtering
  const applyDateFilter = (_data, filter) => {
    if (!filter.startDate && !filter.endDate && filter.preset === "week") {
      // Default behavior - last 7 days
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const filtered_data = _data.filter((item) => {
        const itemDate = new Date(item?.created);
        return itemDate >= weekAgo;
      });

      return filtered_data;
    }

    if (!filter.startDate && !filter.endDate) {
      return _data;
    }

    const startDate = filter.startDate ? new Date(filter.startDate) : null;
    const endDate = filter.endDate ? new Date(filter.endDate) : null;

    const filtered_data = _data.filter((item) => {
      const itemDate = new Date(item?.created);
      if (startDate && itemDate < startDate) return false;
      if (endDate && itemDate > endDate) return false;
      return true;
    });

    return filtered_data;
  };
  // Function to handle date filter changes
  const handleDateFilterChange = (newFilter) => {
    setDateFilter(newFilter);
  };

  // Generate Chart Data
  const generateChartData = (vendors, preset) => {
    const groupBy = {};

    vendors.forEach((vendor) => {
      const date = new Date(vendor.created);
      let key;

      switch (preset) {
        case "today": {
          const hour = date.getHours();
          const period =
            hour === 0
              ? "12 AM"
              : hour < 12
              ? `${hour} AM`
              : hour === 12
              ? "12 PM"
              : `${hour - 12} PM`;
          key = period;
          break;
        }

        case "week": {
          const dayName = date.toLocaleDateString("en-US", { weekday: "long" });
          key = dayName;
          break;
        }

        case "last_month": {
          const day = date.getDate();
          if (day <= 7) key = "Week 1";
          else if (day <= 14) key = "Week 2";
          else if (day <= 21) key = "Week 3";
          else key = "Week 4";
          break;
        }

        case "last_3_months":
        case "last_6_months":
        case "last_year":
        default: {
          const monthName = date.toLocaleDateString("en-US", { month: "long" });
          key = monthName;
          break;
        }
      }

      if (!groupBy[key]) {
        groupBy[key] = { verified: 0, nonVerified: 0 };
      }

      if (vendor.verified) {
        groupBy[key].verified += 1;
      } else {
        groupBy[key].nonVerified += 1;
      }
    });

    // Sorting logic
    const sortOrder = {
      today: [
        "12 AM",
        "1 AM",
        "2 AM",
        "3 AM",
        "4 AM",
        "5 AM",
        "6 AM",
        "7 AM",
        "8 AM",
        "9 AM",
        "10 AM",
        "11 AM",
        "12 PM",
        "1 PM",
        "2 PM",
        "3 PM",
        "4 PM",
        "5 PM",
        "6 PM",
        "7 PM",
        "8 PM",
        "9 PM",
        "10 PM",
        "11 PM",
      ],
      week: [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday",
      ],
      last_month: ["Week 1", "Week 2", "Week 3", "Week 4"],
      default: [
        "January",
        "February",
        "March",
        "April",
        "May",
        "June",
        "July",
        "August",
        "September",
        "October",
        "November",
        "December",
      ],
    };

    const order = sortOrder[preset] || sortOrder.default;

    const result = Object.entries(groupBy)
      .map(([date, counts]) => ({
        date,
        ...counts,
      }))
      .sort((a, b) => order.indexOf(a.date) - order.indexOf(b.date));

    setchartData(result);
  };

  const handleExportCSV = () => {
    if (!filteredData || filteredData.length === 0) {
      toast.warning("No data to export.");
      return;
    }

    // Define CSV headers
    const headers = [
      "Name",
      "Categories",
      "Address",
      "Verified",
      "Created By",
      "Created Date",
    ];

    // Map data rows
    const rows = filteredData.map((vendor) => [
      vendor.name || "",
      vendor.categories?.join(", ") || "",
      vendor.address || "",
      vendor.verified ? "Yes" : "No",
      `${vendor.users?.firstName || ""} ${vendor.users?.lastName || ""}`,
      vendor.created
        ? new Date(vendor.created).toLocaleDateString("en-US", {
            day: "numeric",
            month: "short",
            year: "numeric",
          })
        : "",
    ]);

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map((e) =>
        e
          .map((field) => `"${String(field).replace(/"/g, '""')}"`) // Escape quotes
          .join(",")
      )
      .join("\n");

    // Create a blob and trigger download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.setAttribute("href", url);
    link.setAttribute("download", "vendors_report.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Data fetching
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const records = displayVendors;
        setData(records);
        setDateFilter({ ...dateFilter, preset: "week" });
      } catch (error) {
        console.log("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const filtered_data = applyDateFilter(data, dateFilter);
        switch (filter) {
          case "verified":
            const passing_data = filtered_data.filter((v) => v.verified);
            setFilteredData(passing_data);
            generateChartData(passing_data, dateFilter.preset);
            break;

          case "non-verified":
            const non_verified_data = filtered_data.filter((v) => !v.verified);
            setFilteredData(non_verified_data);
            generateChartData(non_verified_data, dateFilter.preset);
            break;

          default:
            setFilteredData(filtered_data);
            generateChartData(filtered_data, dateFilter.preset);
            break;
        }
      } catch (error) {
        toast.error("Failed to fetch data:");
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [dateFilter, filter]);

  const columns = [
    {
      id: "id",
      accessorKey: "name",
      header: "Vendor Name",
      filterable: true,
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <p>{row.original?.name}</p>
          {row.original?.verified && (
            <Badge
              variant="outline"
              className="ml-2 bg-green-100 dark:bg-green-900/60 text-green-600 border-green-600"
            >
              <CircleCheckBig className="w-4 h-4" />
            </Badge>
          )}
        </div>
      ),
    },
    {
      id: "categories",
      accessorKey: "categories",
      header: "Categories",
      filterable: true,
      cell: ({ row }) => (
        <div className="grid gap-2">
          {row.original?.categories?.map((category, index) => (
            <Badge key={index} variant="secondary" className="mr-1">
              {category}
            </Badge>
          ))}
        </div>
      ),
    },
    {
      id: "address",
      accessorKey: "address",
      header: "Address",
      filterable: true,
      cell: ({ row }) => (
        <div className="truncate w-28">{row.original?.address}</div>
      ),
    },
    {
      id: "createdBy",
      accessorKey: "users.firstName",
      header: "Created By",
      filterable: true,
      cell: ({ row }) => (
        <div>
          {row.original?.users?.firstName} {row.original?.users?.lastName}
        </div>
      ),
    },
    {
      id: "createdDate",
      accessorKey: "created",
      header: "Created Date",
      filterable: false,
      cell: ({ row }) =>
        row.original?.created ? (
          <div>
            {new Date(row.original.created).toLocaleDateString("en-US", {
              day: "numeric",
              month: "short",
              year: "numeric",
            })}
          </div>
        ) : (
          "-"
        ),
    },
    {
      id: "actions",
      accessorKey: "actions",
      header: "Actions",
      filterable: false,
      cell: ({ row }) => <ActionComponent />,
    },
  ];

  return (
    <div className="flex items-center justify-center p-6">
      <div className="w-[90%]">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-bold">Welcome back!</h2>
            <p className="text-sm text-muted-foreground">
              Here's a list of all the Vendors.
            </p>
          </div>

          <div className="flex gap-4 items-end">
            <DateFilter
              dateFilter={dateFilter}
              onFilterChange={handleDateFilterChange}
            />
            {filteredData?.length > 0 && (
              <Button onClick={handleExportCSV}>
                Export CSV <Download className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </div>

        <StatsCard vendors={filteredData} setFilter={setFilter} />
        {/* <GrowthChart chartData={chartData} dateFilter={dateFilter} /> */}
        <DataTable columns={columns} data={filteredData} />
      </div>
    </div>
  );
}
