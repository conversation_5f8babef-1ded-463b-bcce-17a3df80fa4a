import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Activity, Eye, MessageSquare } from "lucide-react";

export default function StatsCard({ engagementData, setFilter }) {
  const [stats, setStats] = useState({
    totalActivities: 0,
    totalPageViews: 0,
    totalEnquiries: 0,
  });

  useEffect(() => {
    const pageViews = engagementData.filter((e) => e.activityType === "page_view");
    const enquiries = engagementData.filter((e) => e.activityType === "enquiry_sent");
    setStats({
      totalActivities: engagementData?.length || 0,
      totalPageViews: pageViews?.length || 0,
      totalEnquiries: enquiries?.length || 0,
    });
  }, [engagementData]);

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Activity className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalActivities?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Activities</div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("page_view")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Eye className="w-5 h-5 text-blue-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalPageViews.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Page Views
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors shimmer duration-300"
        onClick={() => setFilter("enquiry_sent")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <MessageSquare className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalEnquiries?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Enquiries Sent
          </div>
        </div>
      </Card>
    </div>
  );
}
