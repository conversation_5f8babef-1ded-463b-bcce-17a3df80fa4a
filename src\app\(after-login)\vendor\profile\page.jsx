"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Upload, MapPin, Phone, Mail, Globe, Clock } from "lucide-react";
import React from "react";
import { businessProfile } from "@/constants/profiles";

export default function Page() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Business Profile</h1>
          <Button variant="outline" className="rounded-xl">
            <Upload className="w-4 h-4 mr-2" />
            Add Photos
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6 rounded-2xl">
            <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Business Name</label>
                <Input
                  value={businessProfile.name}
                  className="rounded-xl"
                  readOnly
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Input
                  value={businessProfile.category}
                  className="rounded-xl"
                  readOnly
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={businessProfile.description}
                  className="rounded-xl min-h-20"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Services</label>
                <Textarea
                  value={businessProfile.services}
                  className="rounded-xl"
                />
              </div>
            </div>
          </Card>

          <Card className="p-6 rounded-2xl">
            <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Address</label>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <Input
                    value={businessProfile.address}
                    className="rounded-xl"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Phone</label>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-muted-foreground" />
                  <Input value={businessProfile.phone} className="rounded-xl" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-muted-foreground" />
                  <Input value={businessProfile.email} className="rounded-xl" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Website</label>
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-muted-foreground" />
                  <Input
                    value={businessProfile.website}
                    className="rounded-xl"
                  />
                </div>
              </div>
            </div>
          </Card>
        </div>

        <Card className="p-6 rounded-2xl">
          <h3 className="text-lg font-semibold mb-4">Business Hours</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(businessProfile.hours).map(([day, hours]) => (
              <div
                key={day}
                className="flex items-center justify-between p-3 bg-muted/30 rounded-xl"
              >
                <span className="font-medium capitalize">{day}</span>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">{hours}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}
