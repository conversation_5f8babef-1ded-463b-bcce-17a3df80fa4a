import React from "react";
import { Building2, Search, UserPlus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { recentVendors } from "@/constants/vendors";

export default function VendorsProfilePage() {
  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">My Onboarded Vendors</h1>
          <div className="flex gap-2">
            <Button variant="outline" className="rounded-xl">
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
            <Button className="rounded-xl bg-primary hover:bg-primary/90">
              <UserPlus className="w-4 h-4 mr-2" />
              Onboard New
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {recentVendors.map((vendor) => (
            <Card key={vendor.id} className="p-6 rounded-2xl">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                      <Building2 className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">
                        {vendor.businessName}
                      </h3>
                      <p className="text-muted-foreground">
                        Contact: {vendor.contactPerson}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                        <span>{vendor.category}</span>
                        <span>•</span>
                        <span>{vendor.location}</span>
                        <span>•</span>
                        <span>Onboarded: {vendor.onboardedDate}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        vendor.status === "approved" ? "default" : "secondary"
                      }
                      className={
                        vendor.status === "approved"
                          ? "bg-green-100 text-green-800"
                          : "bg-yellow-100 text-yellow-800"
                      }
                    >
                      {vendor.status}
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/30 rounded-xl">
                  <div className="flex items-center gap-4">
                    <div>
                      <span className="text-sm text-muted-foreground">
                        Fee Collected
                      </span>
                      <div className="font-semibold">
                        ${vendor.feeCollected}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">
                        Your Commission
                      </span>
                      <div className="font-semibold text-green-600">
                        ${vendor.commission}
                      </div>
                    </div>
                  </div>
                  <Button size="sm" className="rounded-lg">
                    View Details
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
