"use client";

import React from "react";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { AddToCartButton } from "./AddToCartButton";
import { Star, Heart, Eye } from "lucide-react";

export function ProductCard({ 
  product, 
  showBestSellerBadge = false, 
  showNewBadge = false,
  showQuickView = false,
  variant = "default", // default, compact, list
  className = ""
}) {
  if (variant === "list") {
    return <ProductListCard product={product} className={className} />;
  }

  if (variant === "compact") {
    return <CompactProductCard product={product} className={className} />;
  }

  return (
    <Card className={`group hover:shadow-lg transition-shadow duration-300 ${className}`}>
      <CardContent className="p-0">
        <div className="relative">
          <Link href={`/product/${product.slug}`}>
            <ImageWithFallback
              src={product.images[0]}
              className="w-full h-48 object-cover rounded-t-lg"
              alt={product.name}
            />
          </Link>
          
          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.discount > 0 && (
              <Badge className="bg-red-500 text-white">
                -{product.discount}%
              </Badge>
            )}
            {showNewBadge && product.isNew && (
              <Badge className="bg-green-500 text-white">
                New
              </Badge>
            )}
            {showBestSellerBadge && product.isBestSeller && (
              <Badge className="bg-orange-500 text-white">
                Best Seller
              </Badge>
            )}
          </div>

          {/* Hover Actions */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300 rounded-t-lg">
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col gap-2">
              <Button size="sm" variant="secondary" className="rounded-full p-2">
                <Heart className="w-4 h-4" />
              </Button>
              {showQuickView && (
                <Button size="sm" variant="secondary" className="rounded-full p-2">
                  <Eye className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
        
        <div className="p-4">
          <Link href={`/product/${product.slug}`}>
            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-primary">
              {product.name}
            </h3>
          </Link>
          <p className="text-sm text-gray-600 mb-2">{product.brand}</p>
          
          {/* Rating */}
          <div className="flex items-center gap-2 mb-2">
            <div className="flex items-center">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm text-gray-600 ml-1">
                {product.rating} ({product.reviewCount})
              </span>
            </div>
          </div>
          
          {/* Price */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <span className="text-lg font-bold text-gray-900">
                ${product.price}
              </span>
              {product.originalPrice > product.price && (
                <span className="text-sm text-gray-500 line-through">
                  ${product.originalPrice}
                </span>
              )}
            </div>
          </div>
          
          {/* Add to Cart */}
          <AddToCartButton 
            product={product} 
            size="sm" 
            className="w-full"
          />
        </div>
      </CardContent>
    </Card>
  );
}

// Compact variant for smaller spaces
function CompactProductCard({ product, className = "" }) {
  return (
    <Card className={`group hover:shadow-lg transition-shadow duration-300 ${className}`}>
      <CardContent className="p-3">
        <div className="relative mb-3">
          <Link href={`/product/${product.slug}`}>
            <ImageWithFallback
              src={product.images[0]}
              className="w-full h-32 object-cover rounded-lg"
              alt={product.name}
            />
          </Link>
          {product.discount > 0 && (
            <Badge className="absolute top-1 left-1 bg-red-500 text-white text-xs">
              -{product.discount}%
            </Badge>
          )}
        </div>
        
        <Link href={`/product/${product.slug}`}>
          <h4 className="font-medium text-gray-900 mb-1 line-clamp-2 text-sm hover:text-primary">
            {product.name}
          </h4>
        </Link>
        
        <div className="flex items-center gap-1 mb-2">
          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
          <span className="text-xs text-gray-600">{product.rating}</span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <span className="font-bold text-gray-900 text-sm">
              ${product.price}
            </span>
            {product.originalPrice > product.price && (
              <span className="text-xs text-gray-500 line-through">
                ${product.originalPrice}
              </span>
            )}
          </div>
          <AddToCartButton 
            product={product} 
            size="sm" 
            className="rounded-full p-1"
            showIcon={true}
            children=""
          />
        </div>
      </CardContent>
    </Card>
  );
}

// List variant for list view
function ProductListCard({ product, className = "" }) {
  return (
    <Card className={`group hover:shadow-lg transition-shadow duration-300 ${className}`}>
      <CardContent className="p-4">
        <div className="flex gap-4">
          <div className="relative flex-shrink-0">
            <Link href={`/product/${product.slug}`}>
              <ImageWithFallback
                src={product.images[0]}
                className="w-24 h-24 object-cover rounded-lg"
                alt={product.name}
              />
            </Link>
            {product.discount > 0 && (
              <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs">
                -{product.discount}%
              </Badge>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <Link href={`/product/${product.slug}`}>
                  <h3 className="font-semibold text-gray-900 mb-1 hover:text-primary">
                    {product.name}
                  </h3>
                </Link>
                <p className="text-sm text-gray-600 mb-2">{product.brand}</p>
                <div className="flex items-center gap-4 mb-2">
                  <div className="flex items-center">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm text-gray-600 ml-1">
                      {product.rating} ({product.reviewCount} reviews)
                    </span>
                  </div>
                  {product.isNew && (
                    <Badge className="bg-green-500 text-white text-xs">
                      New
                    </Badge>
                  )}
                  {product.isBestSeller && (
                    <Badge className="bg-orange-500 text-white text-xs">
                      Best Seller
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {product.description}
                </p>
              </div>
              
              <div className="flex flex-col items-end gap-2 ml-4">
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900">
                    ${product.price}
                  </div>
                  {product.originalPrice > product.price && (
                    <div className="text-sm text-gray-500 line-through">
                      ${product.originalPrice}
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button size="sm" variant="outline" className="rounded-full p-2">
                    <Heart className="w-4 h-4" />
                  </Button>
                  <AddToCartButton 
                    product={product} 
                    size="sm" 
                    className="rounded-full px-4"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default ProductCard;
