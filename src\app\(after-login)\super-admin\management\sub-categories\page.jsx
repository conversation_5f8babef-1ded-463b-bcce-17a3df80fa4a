"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Edit, Plus } from "lucide-react";
import React from "react";
import { subCategories } from "@/constants/categories";
import { DataTable } from "@/components/ui/data-table";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import ActionComponent from "@/components/ui/action-component";

const columns = [
  {
    id: "id",
    accessorKey: "name",
    header: "Name",
    filterable: true,
    cell: ({ row }) => (
      <div className="flex items-center gap-1">
        <ImageWithFallback
          src={row.original?.icon}
          className="w-12 rounded-md mr-2"
        />
        <p>{row.original?.name}</p>
      </div>
    ),
  },
  {
    id: "category",
    accessorKey: "categories.name",
    header: "Category",
    filterable: true,
    cell: ({ row }) => <div>{row.original?.categories?.name}</div>,
  },
  {
    id: "coverImage",
    accessorKey: "coverImage",
    header: "Cover Image",
    filterable: false,
    cell: ({ row }) => (
      <ImageWithFallback
        src={row.original?.coverImage}
        className="w-20 rounded-md"
      />
    ),
  },
  {
    id: "isActive",
    accessorKey: "isActive",
    header: "Active",
    filterable: true,
    cell: ({ row }) => (
      <Badge variant={row.original?.isActive ? "default" : "secondary"}>
        {row.original?.isActive ? "Active" : "Inactive"}
      </Badge>
    ),
  },
  {
    id: "slug",
    accessorKey: "slug",
    header: "Slug",
    filterable: true,
    cell: ({ row }) => <div>{row.original?.slug}</div>,
  },
  {
    id: "description",
    accessorKey: "description",
    header: "Description",
    filterable: true,
    cell: ({ row }) => <div>{row.original?.description}</div>,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => <ActionComponent />,
  },
];

export default function SubCategoriesPage() {
  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Sub-Category Management</h1>
        <Button className="rounded-xl bg-primary hover:bg-primary/90">
          <Plus className="w-4 h-4 mr-2" />
          Add Sub-Category
        </Button>
      </div>
      <DataTable columns={columns} data={subCategories} />
    </div>
  );
}
