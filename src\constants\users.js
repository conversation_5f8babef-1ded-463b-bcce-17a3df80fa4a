// System users sample data
/** @type {import('./sampleSchema').SystemUser[]} */
export const systemUsers = [
  { id: 1, name: "<PERSON>",  email: "<EMAIL>",  role: "admin",            status: "active",   lastLogin: "2 hours ago" },
  { id: 2, name: "<PERSON>",email: "<EMAIL>", role: "admin",            status: "active",   lastLogin: "1 day ago" },
  { id: 3, name: "<PERSON>",email: "<EMAIL>",  role: "sales_executive", status: "active",   lastLogin: "3 hours ago" },
  { id: 4, name: "<PERSON>",  email: "<EMAIL>",  role: "sales_executive", status: "inactive", lastLogin: "1 week ago" },
];

