"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Building2, Menu, X, ShoppingCart, LogIn, LogOut } from "lucide-react";
import { usePathname } from "next/navigation";
import { ModeToggle } from "../ui/mode-toggle";

export default function Navbar({
  currentSection,
  onNavigate,
  navigationItems,
}) {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [loggedIn, setLoggedIn] = useState(false);

  const handleLogout = () => {
    localStorage.removeItem("isLoggedIn");
    setLoggedIn(false);
  };

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn");
    if (isLoggedIn) {
      setLoggedIn(true);
    }
  }, [pathname]);

  return (
    <nav className="fixed top-0 left-0 right-0 bg-background/95 backdrop-blur-sm border-b border-border z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link href={"/"} className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary rounded-2xl flex items-center justify-center">
              <Building2 className="w-5 h-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-bold text-foreground">
              Jo bhi Chaho
            </span>
          </Link>

          <div className="hidden md:flex items-center gap-8">
            {navigationItems.map((item) =>
              item.href ? (
                <Link
                  key={item.label}
                  href={item.href}
                  className={`text-sm font-medium transition-colors hover:text-primary ${
                    pathname === item.href
                      ? "text-primary"
                      : "text-muted-foreground"
                  }`}
                >
                  {item.label}
                </Link>
              ) : (
                <button
                  key={item.id}
                  onClick={() => onNavigate(item.id)}
                  className={`text-sm font-medium transition-colors hover:text-primary ${
                    currentSection === item.id
                      ? "text-primary"
                      : "text-muted-foreground"
                  }`}
                >
                  {item.label}
                </button>
              )
            )}
          </div>

          <div className="hidden md:flex items-center gap-4">
            <ModeToggle />
            {loggedIn ? (
              <Button onClick={handleLogout}>
                Sign Out
                <LogOut className="w-4 h-4" />
              </Button>
            ) : (
              <Link href="/login">
                <Button>
                  Sign In
                  <LogIn className="w-4 h-4" />
                </Button>
              </Link>
            )}
            <Link href="/vendors">
              <Button variant="link">Become a Vendor</Button>
            </Link>
          </div>

          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 rounded-xl hover:bg-muted"
          >
            {isMobileMenuOpen ? (
              <X className="w-5 h-5" />
            ) : (
              <Menu className="w-5 h-5" />
            )}
          </button>
        </div>

        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <div className="space-y-4">
              {navigationItems.map((item) =>
                item.href ? (
                  <Link
                    key={item.label}
                    href={item.href}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block w-full text-left px-2 py-2 text-sm font-medium transition-colors hover:text-primary ${
                      pathname === item.href
                        ? "text-primary"
                        : "text-muted-foreground"
                    }`}
                  >
                    {item.label}
                  </Link>
                ) : (
                  <button
                    key={item.id}
                    onClick={() => {
                      onNavigate(item.id);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`block w-full text-left px-2 py-2 text-sm font-medium transition-colors hover:text-primary ${
                      currentSection === item.id
                        ? "text-primary"
                        : "text-muted-foreground"
                    }`}
                  >
                    {item.label}
                  </button>
                )
              )}
              <div className="flex flex-col gap-2 pt-4">
                {loggedIn ? (
                  <Button onClick={handleLogout}>Sign Out</Button>
                ) : (
                  <Link href="/login">
                    <Button>
                      Sign In
                      <LogIn className="w-4 h-4" />
                    </Button>
                  </Link>
                )}
                <Link href="/register">
                  <Button className="rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground">
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
