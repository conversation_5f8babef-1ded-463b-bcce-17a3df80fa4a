"use client";

import { TrendingUp } from "lucide-react";
import {
  Bar,
  BarChart,
  Cell,
  XAxis,
  ReferenceLine,
  ResponsiveContainer,
} from "recharts";
import React from "react";
import { AnimatePresence } from "motion/react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ChartConfig, ChartContainer } from "@/components/ui/chart";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { JetBrains_Mono } from "next/font/google";
import { useMotionValueEvent, useSpring } from "framer-motion";

const jetBrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

const CHART_MARGIN = 35;

// const chartData = [
//   { month: "January", desktop: 342 },
//   { month: "February", desktop: 676 },
//   { month: "March", desktop: 512 },
//   { month: "April", desktop: 629 },
//   { month: "May", desktop: 458 },
//   { month: "June", desktop: 781 },
//   { month: "July", desktop: 394 },
//   { month: "August", desktop: 924 },
//   { month: "September", desktop: 647 },
//   { month: "October", desktop: 532 },
//   { month: "November", desktop: 803 },
//   { month: "December", desktop: 271 },
//   { month: "January", desktop: 342 },
//   { month: "February", desktop: 876 },
//   { month: "March", desktop: 512 },
//   { month: "April", desktop: 629 },
// ];

const chartConfig = {
  count: {
    label: "Enquiries",
    color: "var(--chart-1)",
  },
};

export function GrowthChart({ chartData, dateFilter }) {
  const [activeIndex, setActiveIndex] = React.useState();

  const maxValueIndex = React.useMemo(() => {
    // if user is moving mouse over bar then set value to the bar value
    if (activeIndex !== undefined) {
      return { index: activeIndex, value: chartData[activeIndex].count };
    }

    // if no active index then set value to max value
    return chartData?.reduce(
      (max, data, index) => {
        return data.count > max.value ? { index, value: data.count } : max;
      },
      { index: 0, value: 0 }
    );
  }, [activeIndex, chartData]);

  const maxValueIndexSpring = useSpring(maxValueIndex.value, {
    stiffness: 100,
    damping: 20,
  });

  const [springyValue, setSpringyValue] = React.useState(maxValueIndex.value);

  useMotionValueEvent(maxValueIndexSpring, "change", (latest) => {
    setSpringyValue(Number(latest)?.toFixed(0));
  });

  React.useEffect(() => {
    maxValueIndexSpring.set(maxValueIndex.value);
  }, [maxValueIndex.value, maxValueIndexSpring]);

  return (
    <Card className={"my-4"}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span
            className={cn(jetBrainsMono.className, "text-2xl tracking-tighter")}
          >
            {springyValue}
          </span>
        </CardTitle>
        <CardDescription>
          Showing enquiry count for the {dateFilter.preset}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <AnimatePresence mode="wait">
          <ChartContainer
            config={chartConfig}
            style={{ width: "100%", height: 250 }}
          >
            <ResponsiveContainer width="100%" height={250}>
              <BarChart
                accessibilityLayer
                data={chartData}
                onMouseLeave={() => setActiveIndex(undefined)}
                margin={{
                  left: CHART_MARGIN,
                  top: 10,
                  bottom: 10,
                }}
              >
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                />
                <Bar dataKey="count" fill="var(--chart-2)" radius={4}>
                  {chartData.map((_, index) => (
                    <Cell
                      className="duration-200"
                      opacity={index === maxValueIndex.index ? 1 : 0.2}
                      key={index}
                      onMouseEnter={() => setActiveIndex(index)}
                    />
                  ))}
                </Bar>
                <ReferenceLine
                  opacity={0.4}
                  y={springyValue}
                  stroke="var(--chart-1)"
                  strokeWidth={1}
                  strokeDasharray="3 3"
                  label={<CustomReferenceLabel value={maxValueIndex.value} />}
                />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}

const CustomReferenceLabel = (props) => {
  const { viewBox, value } = props;
  const x = viewBox?.x ?? 0;
  const y = viewBox?.y ?? 0;

  // we need to change width based on value length
  const width = React.useMemo(() => {
    const characterWidth = 8; // Average width of a character in pixels
    const padding = 10;
    return value.toString().length * characterWidth + padding;
  }, [value]);

  return (
    <>
      <rect
        x={x - CHART_MARGIN}
        y={y - 9}
        width={width}
        height={18}
        fill="var(--chart-1)"
        rx={4}
      />
      <text
        fontWeight={600}
        x={x - CHART_MARGIN + 6}
        y={y + 4}
        fill="var(--primary-foreground)"
      >
        {value}
      </text>
    </>
  );
};
